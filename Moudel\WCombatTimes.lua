local addon = ...
local GetTime = GetTime
local InCombatLockdown = InCombatLockdown
local format = format
local _CFG = {}

-- WCombatTimes 模块初始化函数
local function InitWCombatTimes()
    -- 初始化配置
    _G.WCT = _G.WCT or {}
    _G.WCombatTimesDB = _G.WCombatTimesDB or {
        showBanner = true, bannerPosition = {"CENTER", "UIParent", "CENTER", 0, 200},
        enterBannerTitle = "进入战斗", enterBannerLabel = "战斗开始", leaveBannerTitle = "战斗结束",
        bannerDuration = 1.0, playSoundOnEnter = true, playSoundOnLeave = true,
        enterSoundFile = "Interface/AddOns/WanTiny/Textures/Sounds/Join.ogg",
        leaveSoundFile = "Interface/AddOns/WanTiny/Textures/Sounds/leave.ogg",
        framePosition = {"TOP", "UIParent", "TOP", 0, -200}, showMainFrame = true
    }
    _G.WCT.Config = _G.WCombatTimesDB

    -- 配置函数
    _G.WCT.SetConfig = function(key, val) _G.WCT.Config[key] = val; _G.WCombatTimesDB[key] = val end
    _G.WCT.ResetSettings = function()
        local defaults = {showBanner=true, bannerPosition={"CENTER","UIParent","CENTER",0,200}, enterBannerTitle="进入战斗", enterBannerLabel="战斗开始", leaveBannerTitle="战斗结束", bannerDuration=1.0, playSoundOnEnter=true, playSoundOnLeave=true, enterSoundFile="Interface/AddOns/WanTiny/Textures/Join.ogg", leaveSoundFile="Interface/AddOns/WanTiny/Textures/leave.ogg", framePosition={"TOP","UIParent","TOP",0,-200}, showMainFrame=true}
        for k,v in pairs(defaults) do _G.WCT.Config[k]=v; _G.WCombatTimesDB[k]=v end
    end

    -- 创建主框体
    local U1CT = CreateFrame("Frame", "WCombatTimesFrame", UIParent, BackdropTemplateMixin and "BackdropTemplate")
    U1CT:SetSize(90, 27)
    U1CT:SetClampedToScreen(true)
    U1CT:SetPoint(unpack(_G.WCT.Config.framePosition))
    U1CT:SetMovable(true)
    U1CT:SetResizable(true) -- 关键：允许 SetUserPlaced
    U1CT:EnableMouse(true)
    U1CT:RegisterForDrag("LeftButton")
    U1CT:SetUserPlaced(false) -- 必须在 SetMovable/SetResizable 之后
    U1CT:SetScript("OnDragStart", function(self) if not InCombatLockdown() then self:StartMoving() end end)
    U1CT:SetScript("OnDragStop", function(self)
        self:StopMovingOrSizing()
        local point, relativeTo, relativePoint, xOfs, yOfs = self:GetPoint()
        local pos = {point, "UIParent", relativePoint, xOfs, yOfs}
        if _G.WCT and _G.WCT.Config then
            _G.WCT.Config.framePosition = pos
        end
        if _G.WCombatTimesDB then
            _G.WCombatTimesDB.framePosition = {point, "UIParent", relativePoint, xOfs, yOfs}
        end
        self:SetUserPlaced(false)
    end)

    -- 背景（search-select Atlas）
    if U1CT.bg then U1CT.bg:Hide() end
    U1CT.bg = U1CT:CreateTexture(nil, "BACKGROUND")
    U1CT.bg:SetAllPoints()
    U1CT.bg:SetAtlas("search-select")
    U1CT.bg:SetAlpha(0.95)

    -- 计时文本：绿色大号数字，OUTLINE描边
    U1CT.text = U1CT:CreateFontString(nil, "OVERLAY")
    U1CT.text:SetFont(NumberFontNormal:GetFont(), 18, "OUTLINE")
    U1CT.text:SetPoint("CENTER")
    U1CT.text:SetText("0.00")
    U1CT.text:SetTextColor(0, 1, 0)
    U1CT.text:SetShadowColor(0,0,0,0.8)
    U1CT.text:SetShadowOffset(1,-1)

    -- 动画（简单缩放）
    local animGroup = U1CT:CreateAnimationGroup()
    U1CT.Anim = animGroup
    local scale = animGroup:CreateAnimation("Scale")
    scale:SetDuration(0.2)
    scale:SetScaleFrom(0.8, 1)
    scale:SetScaleTo(1, 1)
    local alpha = animGroup:CreateAnimation("Alpha")
    alpha:SetDuration(0.2)
    alpha:SetFromAlpha(0.5)
    alpha:SetToAlpha(1)

    -- 鼠标悬停高亮（背景变亮）
    U1CT:HookScript("OnEnter", function(self)
        if InCombatLockdown() then return end
        self.bg:SetAlpha(1)
    end)
    U1CT:HookScript("OnLeave", function(self)
        self.bg:SetAlpha(0.95)
    end)

    -- 进入战斗
    local function U1CT_Enter(encounter)
        if encounter then U1CT.encounter = true end
        if U1CT.start then return end
        U1CT.StartTimer(true)
        if _CFG["enter_anim"] then U1CT.PlayBanner(true) end
        if _CFG["enter_sound"] then U1CT.PlaySound(true) end
    end
    -- 离开战斗
    local function U1CT_Leave(encounter)
        if not U1CT.start then return end
        if U1CT.encounter and not encounter then return end
        U1CT.encounter = nil
        U1CT.StartTimer(false)
        if _CFG["leave_anim"] then U1CT.PlayBanner(false) end
        if _CFG["leave_sound"] then U1CT.PlaySound(false) end
    end

    U1CT.onUpdate = function(self, elapsed)
        if not InCombatLockdown() then
            -- 只在非BOSS战（encounter为nil）时自动离开
            if not U1CT.encounter then
                U1CT_Leave(false)
            end
            return
        end
        local now = GetTime()
        local combat = now - self.start
        if combat < 60 then
            self.text:SetText(format("%.2f", combat))
        else
            self.text:SetText(format("%d:%04.1f", combat / 60, combat % 60))
        end
    end

    function U1CT.Set(key, val)
        _CFG[key] = val
    end
    function U1CT.PlayBanner(enter)
        local banner = enter and _G["CombatTimerEnterBanner"] or _G["CombatTimerLeaveBanner"]
        if not banner then return end
        banner:ClearAllPoints()
        local pos = _G.WCT.Config.bannerPosition
        if not pos or #pos < 5 then return end
        banner:SetPoint(pos[1], pos[2], pos[3], pos[4], pos[5])
        if enter then
            banner.Title:SetText(_G.WCT.Config.enterBannerTitle or "进入战斗")
            banner.Title:SetTextColor(1, 0, 0) -- 主文字红色
            if banner.BonusLabel then
                banner.BonusLabel:SetText(_G.WCT.Config.enterBannerLabel or "战斗开始")
                banner.BonusLabel:SetTextColor(1, 1, 0) -- 次文字黄色
                -- 让副标题更靠近主标题
                banner.BonusLabel:ClearAllPoints()
                banner.BonusLabel:SetPoint("TOP", banner.Title, "BOTTOM", 0, -2) -- y偏移-2更靠近主标题
            end
        else
            banner.Title:SetText(_G.WCT.Config.leaveBannerTitle or "战斗结束")
            banner.Title:SetTextColor(0, 1, 0) -- 离开主文字绿色
            if banner.BonusLabel then
                banner.BonusLabel:SetText("")
                banner.BonusLabel:ClearAllPoints()
                banner.BonusLabel:SetPoint("TOP", banner.Title, "BOTTOM", 0, -2)
            end
        end
        -- 设置字体为 ARKai_C.TTF
        local fontPath = "Fonts/ARKai_C.TTF"
        if banner.Title then banner.Title:SetFont(fontPath, 32, "OUTLINE") end
        if banner.TitleFlash then banner.TitleFlash:SetFont(fontPath, 32, "OUTLINE") end
        if banner.BonusLabel then banner.BonusLabel:SetFont(fontPath, 24, "OUTLINE") end
        banner:Show()
        -- 1. 只播放入场动画（前0.116秒），然后停留
        if banner.Anim then
            banner.Anim:Stop()
            local function setAllAlpha(a)
                banner:SetAlpha(a)
                if banner.Title then banner.Title:SetAlpha(a) end
                if banner.BonusLabel then banner.BonusLabel:SetAlpha(a) end
                if banner.BG1 then banner.BG1:SetAlpha(a) end
                if banner.BG2 then banner.BG2:SetAlpha(a) end
                if banner.Icon then banner.Icon:SetAlpha(a) end
                if banner.Icon2 then banner.Icon2:SetAlpha(a) end
                if banner.Icon3 then banner.Icon3:SetAlpha(a) end
                if banner.TitleFlash then banner.TitleFlash:SetAlpha(0) end
            end
            banner.Anim:SetToFinalAlpha(false)
            banner.Anim:Play()
            C_Timer.After(0.116, function()
                if banner.Anim:IsPlaying() then banner.Anim:Stop() end
                setAllAlpha(1)
                -- 2. 停留到自定义时间
                C_Timer.After(_G.WCT.Config.bannerDuration or 0.8, function()
                    -- 3. 手动模拟出场动画（淡出+缩小）
                    local fade = banner:CreateAnimationGroup()
                    local alpha = fade:CreateAnimation("Alpha")
                    alpha:SetFromAlpha(1)
                    alpha:SetToAlpha(0)
                    alpha:SetDuration(0.23)
                    alpha:SetOrder(1)
                    local scale = fade:CreateAnimation("Scale")
                    scale:SetScale(0.55, 0.55)
                    scale:SetDuration(0.23)
                    scale:SetOrder(1)
                    fade:SetToFinalAlpha(true)
                    fade:SetScript("OnFinished", function()
                        banner:Hide()
                    end)
                    fade:Play()
                end)
            end)
        else
            -- 无动画时直接定时Hide
            C_Timer.After(_G.WCT.Config.bannerDuration or 0.8, function()
                banner:Hide()
            end)
        end
    end

    function U1CT.PlaySound(enter)
        local ogg, enable = enter and _G.WCT.Config.enterSoundFile or _G.WCT.Config.leaveSoundFile, enter and _G.WCT.Config.playSoundOnEnter or _G.WCT.Config.playSoundOnLeave
        if enable and ogg and ogg ~= "" then
            if type(ogg) == "number" then
                pcall(PlaySound, ogg, "Master")
            else
                pcall(PlaySoundFile, ogg, "Master")
            end
        end
    end
    function U1CT.StartTimer(start)
        if start then
            U1CT.start = GetTime()
            U1CT:SetScript("OnUpdate", U1CT.onUpdate)
            U1CT.text:SetTextColor(1, .2, .2, 1)
            U1CT.Anim:Play()
        else
            U1CT.start = nil
            U1CT.text:SetTextColor(0, 1, 0, 1)
            U1CT:SetScript("OnUpdate", nil)
            U1CT.Anim:Stop()
        end
    end

    -- 配置同步（兼容网易有爱原版设置项）
    _G.WCT.SyncConfigToRuntime = function()
        local cfg = _G.WCT.Config
        _CFG["enter_anim"] = cfg.showBanner
        _CFG["leave_anim"] = cfg.showBanner
        _CFG["enter_sound"] = cfg.playSoundOnEnter
        _CFG["leave_sound"] = cfg.playSoundOnLeave
        _CFG["enter_sound/ogg"] = cfg.enterSoundFile
    end
    _G.WCT.SyncConfigToRuntime()

    -- 注册战斗事件
    for _, event in ipairs({"PLAYER_REGEN_DISABLED", "PLAYER_REGEN_ENABLED", "ENCOUNTER_START", "ENCOUNTER_END"}) do
        U1CT:RegisterEvent(event)
    end
    U1CT:SetScript("OnEvent", function(self, event)
        _G.WCT.SyncConfigToRuntime() -- 每次事件前都同步一次
        if event == "PLAYER_REGEN_DISABLED" or event == "ENCOUNTER_START" then
            U1CT_Enter(event == "ENCOUNTER_START")
        elseif event == "PLAYER_REGEN_ENABLED" or event == "ENCOUNTER_END" then
            U1CT_Leave(event == "ENCOUNTER_END")
        end
    end)

    -- 设置提示信息
    local tooltipLines = {{"左键拖动计时器位置", 0, 1, 0}, {"右键打开计时器设置", 1, 1, 0}, {"SHIFT+左键清除计时", 0.4, 0.6, 1}}
    U1CT:SetScript("OnEnter", function(self)
        if InCombatLockdown() then return end
        GameTooltip:SetOwner(self, "ANCHOR_BOTTOM")
        GameTooltip:SetText("战斗计时器", 1, 0.4, 0.8)
        for _, line in ipairs(tooltipLines) do
            GameTooltip:AddLine(line[1], line[2], line[3], line[4], true)
        end
        GameTooltip:Show()
    end)
    U1CT:SetScript("OnLeave", function(self)
        if GameTooltip:GetOwner() == self then GameTooltip:Hide() end
    end)
    U1CT:SetScript("OnMouseUp", function(self, button)
        if InCombatLockdown() then return end
        if button == "RightButton" then
            -- 打开WanTiny设置面板第4个标签页
            if _G.WanTinyUI and _G.WanTinyUI.ToggleMainFrame then
                _G.WanTinyUI.ToggleMainFrame()
                if _G.WanTinyUI.SelectTab then _G.WanTinyUI.SelectTab(4) end
            end
        elseif button == "LeftButton" and IsShiftKeyDown() then
            -- 清除计时
            U1CT.start = nil
            U1CT.text:SetText("0.00")
            U1CT.text:SetTextColor(0, 1, 0, 1)
            U1CT:SetScript("OnUpdate", nil)
            U1CT.Anim:Stop()
        end
    end)

    -- 加载时恢复主框体位置，优先用 framePosition
    local function RestoreFramePosition()
        local pos = _G.WCT.Config and type(_G.WCT.Config.framePosition) == "table" and _G.WCT.Config.framePosition or nil
        if pos and #pos >= 5 then
            U1CT:ClearAllPoints()
            U1CT:SetUserPlaced(false)
            U1CT:SetPoint(unpack(pos))
        end
    end

    U1CT:RegisterEvent("ADDON_LOADED")
    U1CT:HookScript("OnEvent", function(self, event, arg1)
        if event == "ADDON_LOADED" and arg1 == "WanTiny" then
            -- 恢复主框体位置和显示状态
            RestoreFramePosition()
            if _G.WCT.Config.showMainFrame == false then
                self:Hide()
            else
                self:Show()
            end
        end
    end)

    -- ShowConfigUI函数
    _G.WCT.ShowConfigUI = function()
        if _G.WanTinyUI and _G.WanTinyUI.ToggleMainFrame then
            _G.WanTinyUI.ToggleMainFrame()
            if _G.WanTinyUI.SelectTab then _G.WanTinyUI.SelectTab(4) end
        else print("请先加载WanTiny主界面") end
    end
end

-- 注册到WanTiny模块系统
if _G.WanTiny_RegisterModule then
    _G.WanTiny_RegisterModule("WCombatTimes", InitWCombatTimes)
else
    -- 如果WanTiny还没加载，延迟注册
    local frame = CreateFrame("Frame")
    frame:RegisterEvent("ADDON_LOADED")
    frame:SetScript("OnEvent", function(self, event, addonName)
        if addonName == "WanTiny" and _G.WanTiny_RegisterModule then
            _G.WanTiny_RegisterModule("WCombatTimes", InitWCombatTimes)
            self:UnregisterEvent("ADDON_LOADED")
        end
    end)
end


