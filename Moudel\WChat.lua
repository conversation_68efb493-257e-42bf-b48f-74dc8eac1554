-- 统一的频道配置（移到前面以避免引用错误）
local CHANNEL_CONFIG = {
    -- 默认频道列表
    DEFAULT_CHANNELS = {"综合", "交易", "寻求组队", "大脚世界频道"},
    -- 频道匹配模式和替换文本（修复：正确的捕获组）
    PATTERNS = {
        "%[(%d+)%. 综合.-%]",
        "%[(%d+)%. 交易.-%]",
        "%[(%d+)%. 本地防务.-%]",
        "%[(%d+)%. 寻求组队%]",
        "%[(%d+)%. 世界防务%]",
        "%[(%d+)%. 公会招募.-%]",
        "%[(%d+)%. 大脚世界频道.-%]"
    },
    REPLACEMENTS = {
        "[%1.综]", "[%1.交]", "[%1.防]", "[%1.组]", "[%1.守]", "[%1.招]", "[%1.世]"
    },
    -- 频道映射
    MAPPINGS = {
        ["chnGen"] = "综合",
        ["chnTrade"] = "交易",
        ["chnLFG"] = "寻求组队",
        ["world"] = "大脚世界频道"
    }
}

-- WChat插件主体
local WChat = {
    events = {},
    filters = {},
    DefaultConfig = {
        UseTopInput = true, UseVertical = false, DistanceVertical = 24, DistanceHorizontal = 24,
        AlphaOnLeave = 1.0, EmoteIconListSize = 30, ButtonSize = 22,
        EnableEmoteInput = true, Position = nil, LockChatBar = true, HiddenChannels = {},
        -- 频道缩写配置
        EnableChannelShortNames = true,
        -- 时间戳配置
        EnableTimestamp = true,
        TimestampColor = {r = 255, g = 20, b = 147},
        TimestampFormat = "[%H:%M:%S]"
    }
}

-- 通用工具函数
local Utils = {}

function Utils.GetConfigSafe()
    return pcall(GetConfig) and GetConfig() or nil
end

function Utils.DeepCopy(t)
    if type(t) ~= "table" then return t end
    local c = {}
    for k, v in pairs(t) do
        c[k] = Utils.DeepCopy(v)
    end
    return c
end

-- 前向声明，稍后会被WhisperUtils覆盖
Utils.CreateMovableFrame = function() error("Utils.CreateMovableFrame not yet initialized") end
Utils.SetFrameBackdrop = function() error("Utils.SetFrameBackdrop not yet initialized") end
Utils.CreateTitle = function() error("Utils.CreateTitle not yet initialized") end
Utils.CreateCloseButton = function() error("Utils.CreateCloseButton not yet initialized") end
Utils.CreateMessageFrame = function() error("Utils.CreateMessageFrame not yet initialized") end
Utils.CreateButton = function() error("Utils.CreateButton not yet initialized") end
Utils.CreateCheckbox = function() error("Utils.CreateCheckbox not yet initialized") end

-- 获取配置的辅助函数
local function GetConfig()
    if not WChatClassicDB or type(WChatClassicDB) ~= "table" then WChatClassicDB = {} end
    for key, defaultValue in pairs(WChat.DefaultConfig) do
        if WChatClassicDB[key] == nil then
            WChatClassicDB[key] = Utils.DeepCopy(defaultValue)
        else
            -- 修复时间戳颜色和格式
            if key == "TimestampColor" and type(WChatClassicDB[key]) == "table" then
                local c = WChatClassicDB[key]
                if (c.r and c.g and c.b and c.r <= 1 and c.g <= 1 and c.b <= 1 and c.r == c.g and c.g == c.b) or
                   (c.r == 255 and c.g == 182 and c.b == 193) then
                    WChatClassicDB[key] = Utils.DeepCopy(defaultValue)
                end
            elseif key == "TimestampFormat" and WChatClassicDB[key] == "%H:%M:%S" then
                WChatClassicDB[key] = "[%H:%M:%S]"
            end
        end
    end
    return WChatClassicDB
end

-- 配置管理
local function InitializeConfig()
    GetConfig()
    WChat.db = { profile = WChatClassicDB }
end
-- 事件过滤器管理
WChat.registeredFilters = {}

function WChat:RegisterFilter(event, callback)
    ChatFrame_AddMessageEventFilter(event, callback)
    -- 记录已注册的过滤器以便清理
    if not self.registeredFilters[event] then
        self.registeredFilters[event] = {}
    end
    table.insert(self.registeredFilters[event], callback)
end

function WChat:UnregisterAllFilters()
    for event, callbacks in pairs(self.registeredFilters) do
        for _, callback in ipairs(callbacks) do
            ChatFrame_RemoveMessageEventFilter(event, callback)
        end
    end
    self.registeredFilters = {}
end

-- 事件处理器（需要在InitializeWhisperRecord之前定义）
local EventHandler = {
    frame = nil,
    Register = function(self)
        if self.frame then return end -- 防止重复注册
        self.frame = CreateFrame("Frame")
        for _, event in ipairs({"CHAT_MSG_WHISPER", "CHAT_MSG_WHISPER_INFORM", "CHAT_MSG_BN_WHISPER", "CHAT_MSG_BN_WHISPER_INFORM"}) do
            self.frame:RegisterEvent(event)
        end
        self.frame:SetScript("OnEvent", function(self, event, ...)
            if event == "CHAT_MSG_WHISPER" or event == "CHAT_MSG_BN_WHISPER" then
                if WhisperRecord and WhisperRecord.RecordWhisperMessage then
                    WhisperRecord.RecordWhisperMessage(event, ...)
                end
            elseif event == "CHAT_MSG_WHISPER_INFORM" then
                local message, target, guid = select(1, ...), select(2, ...), select(12, ...)
                if WhisperRecord and WhisperRecord.RecordSentWhisper then
                    WhisperRecord.RecordSentWhisper(message, target, guid)
                end
            elseif event == "CHAT_MSG_BN_WHISPER_INFORM" then
                local message, target, guid, bnSenderID = select(1, ...), select(2, ...), select(12, ...), select(13, ...)
                if WhisperRecord and WhisperRecord.RecordSentWhisper then
                    WhisperRecord.RecordSentWhisper(message, target, guid, true)
                end
            end
        end)
    end,
    Unregister = function(self)
        if self.frame then
            self.frame:UnregisterAllEvents()
            self.frame:SetScript("OnEvent", nil)
            self.frame = nil
        end
    end
}

-- 定时器管理
local WhisperTimers = {
    cleanupTimer = nil,
    classInfoTimer = nil,
    Clear = function(self)
        if self.cleanupTimer then
            self.cleanupTimer:Cancel()
            self.cleanupTimer = nil
        end
        if self.classInfoTimer then
            self.classInfoTimer:Cancel()
            self.classInfoTimer = nil
        end
    end
}

local function InitializeWhisperRecord()
    if WhisperRecord and WhisperRecord.CreateWhisperRecordFrame then
        WhisperRecord.CreateWhisperRecordFrame()
        if EventHandler and EventHandler.Register then
            EventHandler:Register()
        end
        
        -- 清理旧的定时器
        WhisperTimers:Clear()
        
        -- 创建新的定时器
        WhisperTimers.cleanupTimer = C_Timer.NewTicker(3600, function()
            if WhisperRecord.CleanOldRecords then WhisperRecord.CleanOldRecords() end
        end)
        WhisperTimers.classInfoTimer = C_Timer.NewTicker(1, function()
            if WhisperRecord.Utils and WhisperRecord.Utils.ClassInfo then
                WhisperRecord.Utils.ClassInfo:Process()
            end
        end)
    end
end

-- 配置变化响应函数（精简版）
WChat.OnConfigChanged = {}
local function HandleConfigChange(key, value, customHandler)
    GetConfig()[key] = value
    if customHandler then customHandler(value) else WChat:InitChatBar() end
end

-- 批量创建配置处理器
local configHandlers = {
    UseVertical = function(v) WChat:InitChatBar(); if WChat.ChatBar then WChat.ChatBar:Show() end end,
    UseTopInput = function(v)
        if _G.ElvUI then return end
        local editBox = DEFAULT_CHAT_FRAME.editBox
        if editBox then
            editBox:ClearAllPoints()
            local point = v and "BOTTOMLEFT" or "TOPLEFT"
            local relPoint = v and "TOPLEFT" or "BOTTOMLEFT"
            local yOffset = v and 20 or -5
            editBox:SetPoint(point, DEFAULT_CHAT_FRAME, relPoint, 0, yOffset)
            editBox:SetPoint(point:gsub("LEFT", "RIGHT"), DEFAULT_CHAT_FRAME, relPoint:gsub("LEFT", "RIGHT"), 0, yOffset)
        end
    end,
    EmoteIconListSize = function() WChat:InitEmoteTableFrame() end,
    ButtonSize = function() WChat:InitChatBar() end,
    LockChatBar = function(v)
        if WChat.ChatBar then
            SetupChatBarDragging(WChat.ChatBar, not v)
            ChatBarUtils.setBackdrop(not v)
        end
    end
}

-- 简单配置项（无需特殊处理）
local simpleConfigs = {"DistanceHorizontal", "DistanceVertical", "AlphaOnLeave", "EnableEmoteInput", "EnableTimestamp", "EnableChannelShortNames", "TimestampColor", "TimestampFormat"}

-- 批量注册配置处理器
for key, handler in pairs(configHandlers) do
    WChat.OnConfigChanged[key] = function(value) HandleConfigChange(key, value, handler) end
end
for _, key in ipairs(simpleConfigs) do
    WChat.OnConfigChanged[key] = function(value) HandleConfigChange(key, value) end
end

WChat.OnConfigChanged.ResetToDefaults = function()
    -- 重置为默认值（复用GetConfig中的defaults）
    WChatClassicDB = nil
    local newConfig = GetConfig() -- 这会重新创建默认配置
    WChat:InitChatBar()
    WChat:InitEmoteTableFrame()

    -- 恢复所有频道到默认状态（不屏蔽）
    local resetTimer = C_Timer.NewTimer(1, function()
        for _, channelName in ipairs(CHANNEL_CONFIG.DEFAULT_CHANNELS) do
            local channelId = GetChannelName(channelName)
            if channelId and channelId > 0 and not IsChannelShown(channelName) then
                ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, channelName)
            end
        end
        UpdateChannelXIcons()
    end)
    -- 存储定时器引用以便可能的清理
    WChat.resetTimer = resetTimer

end

-- 频道缩写功能（参考SimpleChat的简单实现）
local ChannelShortener = {}



-- 缓存处理结果以提高性能
local messageProcessCache = {}
local cacheMaxSize = 50

-- 聊天消息处理函数（频道缩写 + 时间戳）
function ChannelShortener:ProcessMessage(text, ...)
    local success, config = pcall(GetConfig)
    if not success or not config then return self.OriginalAddMessage(self, text, ...) end
    if type(text) ~= "string" then text = tostring(text or "") end

    if config.EnableChannelShortNames then
        for i = 1, #CHANNEL_CONFIG.PATTERNS do
            local newText = text:gsub(CHANNEL_CONFIG.PATTERNS[i], CHANNEL_CONFIG.REPLACEMENTS[i])
            if newText ~= text then text = newText; break end
        end
        text = text:gsub("%[(%d+)%. .-%]", "%1.")
    end

    -- 处理时间戳
    if config.EnableTimestamp then
        local timeFormat = config.TimestampFormat or "[%H:%M:%S]"
        local timeColor = config.TimestampColor or {r = 255, g = 20, b = 147}

        -- 生成时间戳
        local timestamp = date(timeFormat)

        local r, g, b
        if timeColor.r <= 1 and timeColor.g <= 1 and timeColor.b <= 1 then
            r, g, b = math.floor(timeColor.r * 255), math.floor(timeColor.g * 255), math.floor(timeColor.b * 255)
        else
            r, g, b = math.min(255, math.max(0, math.floor(timeColor.r))), math.min(255, math.max(0, math.floor(timeColor.g))), math.min(255, math.max(0, math.floor(timeColor.b)))
        end
        local colorCode = string.format("ff%02x%02x%02x", r, g, b)

        local originalText = text
        local messageId = originalText:gsub("|c%x%x%x%x%x%x%x%x", ""):gsub("|r", ""):gsub("|", ""):gsub(":", ""):sub(1, 50)

        local clickableTimestamp = "|Hwchat:copy:" .. messageId .. "|h|c" .. colorCode .. timestamp .. "|r|h "



        text = clickableTimestamp .. text
        if not WChat.messageCache then WChat.messageCache, WChat.messageCacheOrder = {}, {} end
        if not WChat.messageCache[messageId] then
            local maxCacheSize = 100
            if #WChat.messageCacheOrder >= maxCacheSize then
                local oldestId = table.remove(WChat.messageCacheOrder, 1)
                WChat.messageCache[oldestId] = nil
            end
            WChat.messageCache[messageId] = originalText
            table.insert(WChat.messageCacheOrder, messageId)
        end
    end

    return self.OriginalAddMessage(self, text, ...)
end

-- 初始化聊天增强功能（频道缩写 + 时间戳）
function ChannelShortener:Initialize()
    local config = GetConfig()
    
    -- 先清理旧的钩子
    self:Cleanup()

    if config.EnableChannelShortNames or config.EnableTimestamp then
        for i = 1, NUM_CHAT_WINDOWS do
            if i ~= 2 then
                local chatFrame = _G['ChatFrame' .. i]
                if chatFrame and chatFrame.AddMessage then
                    chatFrame.OriginalAddMessage = chatFrame.AddMessage
                    chatFrame.AddMessage = self.ProcessMessage
                end
            end
        end
    end
end

-- 清理聊天框架钩子
function ChannelShortener:Cleanup()
    for i = 1, NUM_CHAT_WINDOWS do
        local chatFrame = _G['ChatFrame' .. i]
        if chatFrame and chatFrame.OriginalAddMessage then
            chatFrame.AddMessage = chatFrame.OriginalAddMessage
            chatFrame.OriginalAddMessage = nil
        end
    end
end

-- WChat模块清理函数
function WChat:Cleanup()
    -- 清理定时器
    if WhisperTimers then
        WhisperTimers:Clear()
    end
    
    -- 清理其他定时器
    if self.resetTimer then
        self.resetTimer:Cancel()
        self.resetTimer = nil
    end
    if self.whisperInitTimer then
        self.whisperInitTimer:Cancel()
        self.whisperInitTimer = nil
    end
    if self.elvuiTimer then
        self.elvuiTimer:Cancel()
        self.elvuiTimer = nil
    end
    if self.elvuiFrame then
        self.elvuiFrame:UnregisterAllEvents()
        self.elvuiFrame:SetScript("OnEvent", nil)
        self.elvuiFrame = nil
    end
    
    -- 清理事件处理器
    if EventHandler then
        EventHandler:Unregister()
    end
    
    -- 清理事件过滤器
    self:UnregisterAllFilters()
    
    -- 清理聊天框架钩子
    if ChannelShortener then
        ChannelShortener:Cleanup()
    end
    
    -- 清理聊天条事件
    if self.ChatBar then
        self.ChatBar:UnregisterAllEvents()
        self.ChatBar:SetScript("OnEvent", nil)
    end
    
    -- 清理表情框架
    if EmoteTableFrame then
        EmoteTableFrame:Hide()
        if EmoteTableFrame.clickCatcher then
            EmoteTableFrame.clickCatcher:Hide()
            EmoteTableFrame.clickCatcher:SetParent(nil)
            EmoteTableFrame.clickCatcher = nil
        end
        EmoteTableFrame:SetParent(nil)
        EmoteTableFrame = nil
    end
    
    -- 清理聊天复制框架
    if chatCopyFrame then
        chatCopyFrame:Hide()
        chatCopyFrame:SetParent(nil)
    end
    
    -- 清理密语记录框架
    if WhisperRecord and WhisperRecord.WanMY then
        WhisperRecord.WanMY:Hide()
        WhisperRecord.WanMY:SetParent(nil)
        WhisperRecord.WanMY = nil
    end
    
    -- 清理缓存
    if self.messageCache then
        self.messageCache = nil
        self.messageCacheOrder = nil
    end
    
    -- 重置标志
    self.emoteEventsRegistered = false
end

-- WChat模块初始化函数
local function InitWChatModule()
    -- 先清理旧资源，防止重复初始化导致内存泄漏
    if _G.WChat and _G.WChat.Cleanup then
        _G.WChat:Cleanup()
    end
    
    InitializeConfig()

    -- 初始化频道缩写功能
    ChannelShortener:Initialize()

    WChat:InitChatBar()
    WChat:InitEmoteTableFrame()
    WChat:InitChatSkin()

    _G.WChat = WChat

    -- 使用可取消的定时器初始化密语记录
    WChat.whisperInitTimer = C_Timer.NewTimer(1, function() 
        if InitializeWhisperRecord then InitializeWhisperRecord() end 
    end)
    
    local function ApplyElvUIFix()
        if _G.ElvUI then
            local E = unpack(_G.ElvUI)
            local CH = E:GetModule('Chat')
            if CH then
                if CH.StyleChat then
                    for _, name in ipairs(_G.CHAT_FRAMES) do
                        local frame = _G[name]
                        if frame then CH:StyleChat(frame) end
                    end
                end
                if CH.UpdateEditboxAnchors then CH:UpdateEditboxAnchors() end
            end
        end
    end

    if _G.ElvUI then 
        WChat.elvuiTimer = C_Timer.NewTimer(2, ApplyElvUIFix)
    else
        WChat.elvuiFrame = CreateFrame("Frame")
        WChat.elvuiFrame:RegisterEvent("ADDON_LOADED")
        WChat.elvuiFrame:SetScript("OnEvent", function(self, event, addonName)
            if addonName == "ElvUI" then
                WChat.elvuiTimer = C_Timer.NewTimer(1, ApplyElvUIFix)
                self:UnregisterEvent("ADDON_LOADED")
                WChat.elvuiFrame = nil
            end
        end)
    end

end

if WanTiny_RegisterModule then WanTiny_RegisterModule("WChat", InitWChatModule) end

-- 聊天条功能

-- 常量
local EMOTION_PATH = "Interface/AddOns/WanTiny/Textures/Emotion"
local TEXTURE_PATH = "Interface/AddOns/WanTiny/Textures"

local CLASS_COLORS = {
    ["WARRIOR"] = {r = 0.78, g = 0.61, b = 0.43}, ["PALADIN"] = {r = 0.96, g = 0.55, b = 0.73},
    ["HUNTER"] = {r = 0.67, g = 0.83, b = 0.45}, ["ROGUE"] = {r = 1.0, g = 0.96, b = 0.41},
    ["PRIEST"] = {r = 1.0, g = 1.0, b = 1.0}, ["SHAMAN"] = {r = 0.0, g = 0.44, b = 0.87},
    ["MAGE"] = {r = 0.41, g = 0.8, b = 0.94}, ["WARLOCK"] = {r = 0.58, g = 0.51, b = 0.79},
    ["DRUID"] = {r = 1.0, g = 0.49, b = 0.04}, ["DEATHKNIGHT"] = {r = 0.77, g = 0.12, b = 0.23},
    ["BN_2"] = {r = 0, g = 1, b = 0.9647}
}
local CLASS_ICON_COORDS = {
    ["WARRIOR"] = {0, 0.25, 0, 0.25}, ["MAGE"] = {0.25, 0.49609375, 0, 0.25},
    ["ROGUE"] = {0.49609375, 0.7421875, 0, 0.25}, ["DRUID"] = {0.7421875, 0.98828125, 0, 0.25},
    ["HUNTER"] = {0, 0.25, 0.25, 0.5}, ["SHAMAN"] = {0.25, 0.49609375, 0.25, 0.5},
    ["PRIEST"] = {0.49609375, 0.7421875, 0.25, 0.5}, ["WARLOCK"] = {0.7421875, 0.98828125, 0.25, 0.5},
    ["PALADIN"] = {0, 0.25, 0.5, 0.75}, ["DEATHKNIGHT"] = {0.25, 0.49609375, 0.5, 0.75}
}
-- 工具提示配置（简化版）
local TOOLTIP_CONFIG = {
    emote = {"打开表情面板"}, say = {"说话"}, yell = {"大喊"}, party = {"小队聊天"},
    guild = {"公会聊天"}, raid = {"团队聊天"}, bg = {"战场/副本聊天"},
    chnGen = {"加入/发言", "切换频道消息显示/屏蔽"}, chnTrade = {"加入/发言", "切换频道消息显示/屏蔽"},
    chnLFG = {"加入/发言", "切换频道消息显示/屏蔽"}, world = {"加入/发言", "切换频道消息显示/屏蔽"},
    roll = {"随机Roll点", "打开战利品投骰界面"}, gold = {"打开金团表"}, ai = {"打开AI助手"},
    atlas = {"打开副本掉落/AtlasLoot"}, chatcopy = {"聊天内容复制"},
    minimize = {"聊天窗口最小化/还原"}, whisper = {"密语记录/未读高亮"}
}

-- 动态生成工具提示
local TOOLTIPS = {}
for key, actions in pairs(TOOLTIP_CONFIG) do
    local tooltip = "|cff00ffff鼠标左键|r-|cffff80ff" .. actions[1] .. "|r"
    if actions[2] then tooltip = tooltip .. "\n|cff00ffff鼠标右键|r-|cffff80ff" .. actions[2] .. "|r" end
    TOOLTIPS[key] = tooltip
end

-- MeetingHorn特殊处理
TOOLTIPS.meetinghorn = function()
    local tooltip = "|cff00ffff鼠标左键|r-|cffff80ff打开集结号|r"
    if IsAddOnLoaded("MeetingHorn") then
        local addon = LibStub and LibStub('AceAddon-3.0'):GetAddon('MeetingHorn', true)
        local lfg = addon and addon:GetModule('LFG', true)
        if lfg then
            local icon1, icon2 = "|TInterface\\AddOns\\MeetingHorn\\Media\\DataBroker:16:16:0:0:64:32:0:32:0:32|t", "|TInterface\\AddOns\\MeetingHorn\\Media\\DataBroker:16:16:0:0:64:32:32:64:0:32|t"
            tooltip = tooltip .. "\n" .. icon2 .. "|cffFFD700活动数量: " .. lfg:GetActivityCount() .. "|r"
            local count = lfg:GetCurrentActivity() and lfg:GetApplicantCount() or lfg:GetApplicationCount()
            local label = lfg:GetCurrentActivity() and "申请者数量" or "申请数量"
            tooltip = tooltip .. "\n" .. icon1 .. "|cffFFD700" .. label .. ": " .. count .. "|r"
        else tooltip = tooltip .. "\n|cffff8080模块未找到|r" end
    else tooltip = tooltip .. "\n|cffff8080插件未加载|r" end
    return tooltip
end

-- 工具函数
local function SetStandardBackdrop(frame, bgAlpha, borderAlpha, backdropType)
    local backdrop = {
        bgFile = "Interface/Tooltips/UI-Tooltip-Background",
        edgeFile = "Interface/Tooltips/UI-Tooltip-Border",
        tile = true, tileSize = 16, edgeSize = 16,
        insets = { left = 4, right = 4, top = 4, bottom = 4 }
    }
    if backdropType == "emote" then
        backdrop.bgFile = "Interface\\Buttons\\WHITE8x8"
        backdrop.insets = { left = 3, right = 3, top = 3, bottom = 3 }
    elseif backdropType == "dialog" then
        backdrop.bgFile = "Interface/DialogFrame/UI-DialogBox-Background"
        backdrop.edgeFile = "Interface/DialogFrame/UI-DialogBox-Border"
    end

    if not frame.SetBackdrop then Mixin(frame, BackdropTemplateMixin) end
    frame:SetBackdrop(backdrop)
    frame:SetBackdropColor(0, 0, 0, bgAlpha or 0.8)
    frame:SetBackdropBorderColor(0.5, 0.5, 0.5, borderAlpha or 1)
end

local function GetClassColorCode(className)
    local c = CLASS_COLORS[className]
    return c and string.format("|cff%02x%02x%02x", c.r*255, c.g*255, c.b*255) or "|cffffffff"
end

local function GetDisplayName(playerName, keepRealm)
    if not playerName or string.find(playerName, "#") then return playerName or "" end
    if keepRealm then return playerName end
    return strsplit("-", playerName) or playerName
end

local IsMovable = false
local ChatBar = CreateFrame("Frame", nil, UIParent, "BackdropTemplate")

WChat.ChatBar = ChatBar



local function HandleChannelAction(channelName, openChat)
    local chnId = GetChannelName(channelName)
    if not chnId or chnId == 0 then
        JoinPermanentChannel(channelName)
        ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, channelName)
        if openChat then
            C_Timer.After(0.2, function()
                local newId = GetChannelName(channelName)
                if newId and newId > 0 then
                    ChatFrame_OpenChat("/" .. newId .. " " .. DEFAULT_CHAT_FRAME.editBox:GetText(), DEFAULT_CHAT_FRAME)
                end
            end)
        end
    else
        if openChat then
            ChatFrame_OpenChat("/" .. chnId .. " " .. DEFAULT_CHAT_FRAME.editBox:GetText(), DEFAULT_CHAT_FRAME)
        else
            LeaveChannelByName(channelName)
        end
    end
end

local function OpenChatWithCommand(command)
    ChatFrame_OpenChat(command .. " " .. DEFAULT_CHAT_FRAME.editBox:GetText(), DEFAULT_CHAT_FRAME)
end

-- 改为全局函数，避免作用域问题
function IsChannelShown(channelName)
    local channels = {GetChatWindowChannels(DEFAULT_CHAT_FRAME:GetID() or 1)}
    for i = 1, #channels, 2 do
        if channels[i] == channelName then return true end
    end
    return false
end

local function ToggleChannelShowHide(channelName)
    local config = GetConfig()
    if IsChannelShown(channelName) then
        ChatFrame_RemoveChannel(DEFAULT_CHAT_FRAME, channelName)
        config.HiddenChannels[channelName] = true
    else
        ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, channelName)
        config.HiddenChannels[channelName] = nil
    end
end



-- 改为全局函数，避免作用域问题
function UpdateChannelXIcons()
    if not ChatBar then return end

    local children = {ChatBar:GetChildren()}
    for _, child in pairs(children) do
        if child.X and child.buttonName and CHANNEL_CONFIG.MAPPINGS[child.buttonName] then
            local channelName = CHANNEL_CONFIG.MAPPINGS[child.buttonName]
            local isShown = IsChannelShown(channelName)
            child.X:SetShown(not isShown)
        end
    end
end

-- 为频道按钮创建切换处理器
local function CreateChannelToggleHandler(channelName)
    return function(self, button)
        if button == "LeftButton" then HandleChannelAction(channelName, true)
        else ToggleChannelShowHide(channelName); C_Timer.After(0.1, UpdateChannelXIcons) end
    end
end

-- 统一的点击处理器（合并所有类型）
local ClickHandlers = {
    -- 基础频道
    say = function() OpenChatWithCommand("/s") end,
    yell = function() OpenChatWithCommand("/y") end,
    party = function() OpenChatWithCommand("/p") end,
    guild = function() OpenChatWithCommand("/g") end,
    raid = function() OpenChatWithCommand("/raid") end,
    bg = function(self, button)
        local inBG = UnitInBattleground("player")
        local inInstance, instanceType = IsInInstance()
        if inBG then OpenChatWithCommand("/bg")
        elseif inInstance and (instanceType == "party" or instanceType == "raid") then OpenChatWithCommand("/i") end
    end,
    
    -- 频道按钮
    ["综合"] = CreateChannelToggleHandler("综合"),
    ["交易"] = CreateChannelToggleHandler("交易"),
    ["寻求组队"] = CreateChannelToggleHandler("寻求组队"),
    ["大脚世界频道"] = CreateChannelToggleHandler("大脚世界频道"),
    
    -- 特殊功能
    emote = function() WChat:ToggleEmoteTable() end,
    roll = function(self, button)
        if button == "RightButton" then ToggleLootHistoryFrame() else RandomRoll(1, 100) end
    end,
    chatcopy = function() WChat:CopyFunc() end,
    minimize = function(self, button)
        local btn = _G.WChat_ChatHideButton
        if btn and btn:GetScript("OnClick") then
            btn:Click()
            C_Timer.After(0.05, UpdateMinimizeButtonText)
        end
    end,
    whisper = function()
        if WhisperRecord and WhisperRecord.ToggleWhisperRecordFrame then 
            WhisperRecord.ToggleWhisperRecordFrame() 
        end
    end
}

-- 批量注册频道处理器
for _, channelName in ipairs(CHANNEL_CONFIG.DEFAULT_CHANNELS) do
    local key = CHANNEL_CONFIG.MAPPINGS[channelName] or channelName:lower():gsub("[^%w]", "")
    ClickHandlers[key] = CreateChannelToggleHandler(channelName)
end

-- 聊天条设置函数（合并）
local ChatBarUtils = {
    savePosition = function()
        local chatBar = WChat.ChatBar or ChatBar
        if chatBar then
            local point, relativeTo, relativePoint, xOfs, yOfs = chatBar:GetPoint()
            GetConfig().Position = {point = point, relativeTo = relativeTo and relativeTo:GetName() or nil, relativePoint = relativePoint, xOfs = xOfs, yOfs = yOfs}
        end
    end,
    setBackdrop = function(enabled)
        local chatBar = WChat.ChatBar or ChatBar
        if chatBar then
            if enabled then SetStandardBackdrop(chatBar, 0.8, 1, "dialog") else chatBar:SetBackdrop({}) end
        end
    end
}

local function SetupChatBarDragging(chatBar, enabled)
    if not chatBar then return end
    chatBar:SetMovable(enabled)
    chatBar:EnableMouse(enabled)
    if enabled then
        chatBar:RegisterForDrag("LeftButton")
        chatBar:SetScript("OnDragStart", chatBar.StartMoving)
        chatBar:SetScript("OnDragStop", function(self) self:StopMovingOrSizing(); ChatBarUtils.savePosition() end)
    else
        chatBar:SetScript("OnDragStart", nil)
        chatBar:SetScript("OnDragStop", nil)
    end
end

local function Movelock_OnClick(self, button)
    if button == "LeftButton" then
        IsMovable = not IsMovable

        ChatBarUtils.setBackdrop(IsMovable)
        if not IsMovable then ChatBarUtils.savePosition() end
        ChatBar:EnableMouse(IsMovable)
    elseif button == "MiddleButton" and IsMovable then
        ChatBar:ClearAllPoints()
        ChatBar:SetPoint("BOTTOMLEFT", UIParent, "BOTTOMLEFT", 0, 0)
    end
end

local BGButtonFrame
WChat.BGButtonFrame = nil

-- 改为全局函数，避免作用域问题
function UpdateBGButtonText()
    if not BGButtonFrame or not BGButtonFrame.text then return end
    local inBG = UnitInBattleground("player")
    BGButtonFrame.text:SetText(inBG and "战" or "副")
end

-- ChannelBGOrLFG_OnClick 函数已内联到 ClickHandlers 中

-- 改为全局函数，避免作用域问题
function UpdateMinimizeButtonText()
    if not ChatBar or not ChatBar.MinimizeBtn then return end
    local btn = _G.WChat_ChatHideButton
    local shown = _G.ChatFrame1 and _G.ChatFrame1:IsVisible()
    if shown then
        ChatBar.MinimizeBtn.text:SetText("隐")
    else
        ChatBar.MinimizeBtn.text:SetText("显")
    end
end

-- ChatMinimize_OnClick 函数已内联到 ClickHandlers 中

local whisperButtonFrame, whisperFlashTimer
WChat.whisperButtonFrame = nil

local function HasUnreadWhispers()
    local db = _G.WChatClassicWhisperDB
    if not (db and db.record and db.record[1]) then return false end
    for _, playerData in pairs(db.record[1]) do
        if playerData and playerData.hasUnread then return true end
    end
    return false
end

local function UpdateWhisperButtonFlash()
    if not whisperButtonFrame then return end
    if whisperFlashTimer then whisperFlashTimer:Cancel(); whisperFlashTimer = nil end
    if HasUnreadWhispers() then
        local isBright = true
        whisperFlashTimer = C_Timer.NewTicker(0.6, function()
            isBright = not isBright
            whisperButtonFrame:SetAlpha(isBright and 1 or 0.1)
        end)
    end
    whisperButtonFrame:SetAlpha(1)
end

-- WhisperRecord_OnClick 函数已内联到 ClickHandlers 中

local function CreateAddonOnClick(addonName, globalVar, slashCmd)
    return function()
        if not IsAddOnLoaded(addonName) then LoadAddOn(addonName) end
        if slashCmd and SlashCmdList[slashCmd] then SlashCmdList[slashCmd]("")
        elseif globalVar then local addon = _G[globalVar] if addon and addon.MainFrame then addon.MainFrame:SetShown(not addon.MainFrame:IsVisible()) end end
    end
end

local Gold_OnClick, AI_OnClick, Atlas_OnClick = CreateAddonOnClick("BiaoGe", "BG"), CreateAddonOnClick("BiaoGeAI", "BGAI"), CreateAddonOnClick("AtlasLootClassic", nil, "ATLASLOOT")
local MeetingHorn_OnClick = CreateAddonOnClick("MeetingHorn", "MeetingHorn")

-- 添加插件按钮处理函数到ClickHandlers
ClickHandlers.gold = Gold_OnClick
ClickHandlers.ai = AI_OnClick
ClickHandlers.atlas = Atlas_OnClick
ClickHandlers.meetinghorn = MeetingHorn_OnClick

-- 调试函数：检查ClickHandlers完整性
local function DebugClickHandlers()
    print("WChat: 检查ClickHandlers完整性...")
    local requiredActions = {"bg", "minimize", "whisper", "meetinghorn", "gold", "ai", "atlas"}
    for _, action in ipairs(requiredActions) do
        if ClickHandlers[action] then
            print("WChat: ✓ " .. action .. " 处理函数已设置")
        else
            print("WChat: ✗ " .. action .. " 处理函数缺失")
        end
    end
end

-- 在聊天条初始化时调用调试函数
C_Timer.After(2, DebugClickHandlers)

-- MeetingHorn按钮点击处理函数
-- 统一的按钮配置表
local BUTTON_CONFIG = {
    -- 基础按钮
    {"emote", "|TInterface/AddOns/WanTiny/Textures/Emotion/excited.tga:14:14:0:0|t", nil, "emote"},
    {"say", "说", {1.00, 1.00, 1.00}, "say"},
    {"yell", "喊", {1.00, 0.25, 0.25}, "yell"},
    {"party", "队", {0.66, 0.66, 1.00}, "party"},
    {"guild", "会", {0.25, 1.00, 0.25}, "guild"},
    {"raid", "团", {1.00, 0.50, 0.00}, "raid"},
    {"bg", "战", {1.00, 0.50, 0.00}, "bg"},
    {"chnGen", "综", {0.82, 0.70, 0.55}, "综合"},
    {"chnTrade", "交", {0.82, 0.70, 0.55}, "交易"},
    {"chnLFG", "寻", {0.82, 0.70, 0.55}, "寻求组队"},
    {"world", "世", {0.78, 1.00, 0.59}, "大脚世界频道"},
    {"roll", "|TInterface/AddOns/WanTiny/Textures/Icon/roll:16:16:0:0|t", {1.00, 1.00, 0.00}, "roll"},
    {"chatcopy", "复", {0.20, 0.60, 0.80}, "chatcopy"},
    {"minimize", "最", {1.00, 0.84, 0.00}, "minimize"},
    {"whisper", "|TInterface/ChatFrame/UI-ChatWhisperIcon:16:16:0:0|t", {1.00, 0.50, 1.00}, "whisper"}
}

-- 插件按钮配置
local ADDON_BUTTONS = {
    {"BiaoGe", "gold", "金", {1.00, 0.84, 0.00}},
    {"BiaoGeAI", "ai", "AI", {0.20, 0.80, 0.80}},
    {"AtlasLootClassic", "atlas", "Interface/AddOns/WanTiny/Textures/Icon/Atlas.blp", {0.20, 0.80, 0.20}},
    {"MeetingHorn", "meetinghorn", "Interface/AddOns/WanTiny/Textures/Icon/Lfg.blp", {0.00, 1.00, 1.00}}
}

-- 动态生成按钮列表
local function GenerateChannelButtons()
    local buttons = {}
    for _, cfg in ipairs(BUTTON_CONFIG) do
        table.insert(buttons, {name = cfg[1], text = cfg[2], color = cfg[3], action = cfg[4]})
    end
    -- 插入插件按钮到倒数第三个位置
    local insertPos = #buttons - 2
    for _, addon in ipairs(ADDON_BUTTONS) do
        if IsAddOnLoaded(addon[1]) then
            table.insert(buttons, insertPos, {name = addon[2], text = addon[3], color = addon[4], action = addon[2]})
            insertPos = insertPos + 1
        end
    end
    return buttons
end

-- 统一的按钮创建函数
local function CreateChannelButton(data, index, config)
    -- 确保config不为nil
    config = config or GetConfig()
    local frame = CreateFrame("Button", data.name, ChatBar)
    local buttonSize = config.ButtonSize or 22
    frame:SetSize(buttonSize, buttonSize)
    frame:SetAlpha(config.AlphaOnLeave or 0.6)
    frame:RegisterForClicks("AnyUp")
    
    -- 调试信息和更健壮的点击处理
    local clickHandler = ClickHandlers[data.action]
    if not clickHandler then
        print("WChat: 按钮 '" .. (data.text or data.name) .. "' (action: " .. (data.action or "nil") .. ") 没有找到对应的处理函数")
        clickHandler = function() print("WChat: 按钮点击但无处理函数: " .. (data.action or "unknown")) end
    end
    
    frame:SetScript("OnClick", function(self, button)
        if clickHandler then
            local success, err = pcall(clickHandler, self, button)
            if not success then
                print("WChat: 按钮点击处理出错: " .. (err or "未知错误"))
            end
        end
    end)
    
    -- 鼠标事件
    frame:SetScript("OnEnter", function(self)
        self:SetAlpha(0.5)
        local tooltip = TOOLTIPS[data.name]
        if tooltip then
            GameTooltip:SetOwner(self, "ANCHOR_RIGHT")
            GameTooltip:SetText(type(tooltip) == "function" and tooltip() or tooltip)
            GameTooltip:Show()
        end
    end)
    frame:SetScript("OnLeave", function(self)
        local cfg = GetConfig()
        self:SetAlpha(cfg and cfg.AlphaOnLeave or 0.6)
        GameTooltip:Hide()
    end)

    -- 创建文本或纹理
    frame.text = frame:CreateFontString(nil, "ARTWORK")
    frame.text:SetFont(STANDARD_TEXT_FONT, math.max(10, math.min(20, buttonSize * 0.68)), "OUTLINE")
    frame.text:SetPoint("CENTER", 0, 0)
    
    -- 特殊纹理按钮处理
    if (data.name == "meetinghorn" or data.name == "atlas") and not string.find(data.text, "|T") then
        frame.texture = frame:CreateTexture(nil, "ARTWORK")
        frame.texture:SetTexture(data.text)
        frame.texture:SetSize(buttonSize, buttonSize)
        frame.texture:SetPoint("CENTER", 0, 0)
        if data.color then frame.texture:SetVertexColor(unpack(data.color)) end
        frame.text:SetText("")
    else
        frame.text:SetText(data.text)
        if data.color and not string.find(data.text, "|c") then
            frame.text:SetTextColor(unpack(data.color))
        end
    end

    -- 频道屏蔽图标
    if CHANNEL_CONFIG.MAPPINGS[data.name] then
        frame.X = frame:CreateTexture(nil, "OVERLAY")
        frame.X:SetTexture("interface/common/voicechat-muted.blp")
        frame.X:SetSize(math.max(12, buttonSize * 0.6), math.max(12, buttonSize * 0.6))
        frame.X:SetAlpha(0.7)
        frame.X:SetPoint("CENTER")
        frame.X:SetDrawLayer("OVERLAY", 7)
        frame.X:Hide()
    end

    frame.buttonName = data.name
    frame:SetPoint((config.UseVertical and "TOP" or "LEFT"), ChatBar, (config.UseVertical and "TOP" or "LEFT"),
        config.UseVertical and 0 or (10 + (index - 1) * (config.DistanceHorizontal or 24)),
        config.UseVertical and ((1 - index) * (config.DistanceVertical or 24)) or 0)

    -- 特殊按钮引用
    local specialButtons = {bg = "BGButtonFrame", minimize = "MinimizeBtn", whisper = "whisperButtonFrame", emote = "emoteButtonFrame"}
    if specialButtons[data.name] then
        if data.name == "bg" then BGButtonFrame, WChat.BGButtonFrame = frame, frame; UpdateBGButtonText()
        elseif data.name == "minimize" then ChatBar.MinimizeBtn = frame; C_Timer.After(0.1, UpdateMinimizeButtonText)
        elseif data.name == "whisper" then whisperButtonFrame, WChat.whisperButtonFrame = frame, frame
        elseif data.name == "emote" then WChat.emoteButtonFrame = frame end
    end
end

function WChat:InitChatBar()
    local config = GetConfig()
    local channelButtons = GenerateChannelButtons()
    
    ChatBar:SetFrameLevel(0)
    
    -- 清理现有按钮
    for _, child in pairs({ChatBar:GetChildren()}) do
        if child.buttonName then child:Hide(); child:SetParent(nil) end
    end
    
    WChat.emoteButtonFrame, BGButtonFrame, WChat.BGButtonFrame, whisperButtonFrame, WChat.whisperButtonFrame, ChatBar.MinimizeBtn = nil, nil, nil, nil, nil, nil
    
    -- 设置聊天条尺寸
    local buttonSize, padding = config.ButtonSize or 22, 8
    local width = config.UseVertical and (buttonSize + padding) or (#channelButtons * config.DistanceHorizontal + 10)
    local height = config.UseVertical and (#channelButtons * config.DistanceVertical + 10) or (buttonSize + padding)
    ChatBar:SetSize(width, height)
    
    -- 设置输入框位置（非ElvUI）
    if not _G.ElvUI then
        DEFAULT_CHAT_FRAME.editBox:ClearAllPoints()
        local anchor = config.UseTopInput and {"BOTTOMLEFT", "TOPLEFT", 0, 20, "BOTTOMRIGHT", "TOPRIGHT"} or {"TOPLEFT", "BOTTOMLEFT", 0, -5, "TOPRIGHT", "BOTTOMRIGHT"}
        DEFAULT_CHAT_FRAME.editBox:SetPoint(anchor[1], DEFAULT_CHAT_FRAME, anchor[2], anchor[3], anchor[4])
        DEFAULT_CHAT_FRAME.editBox:SetPoint(anchor[5], DEFAULT_CHAT_FRAME, anchor[6], anchor[3], anchor[4])
    end
    
    -- 设置聊天条位置
    ChatBar:ClearAllPoints()
    if config.Position and config.Position.point then
        local pos = config.Position
        ChatBar:SetPoint(pos.point, pos.relativeTo or UIParent, pos.relativePoint, pos.xOfs, pos.yOfs)
    else
        ChatBar:SetPoint("TOPLEFT", DEFAULT_CHAT_FRAME, "TOPLEFT", 0, 90)
    end
    
    SetupChatBarDragging(ChatBar, not config.LockChatBar)
    ChatBarUtils.setBackdrop(not config.LockChatBar)
    
    -- 创建按钮
    for i, button in ipairs(channelButtons) do CreateChannelButton(button, i, config) end
    
    -- 设置频道
    ChatFrame_RemoveMessageGroup(DEFAULT_CHAT_FRAME, "CHANNEL")
    for _, channelName in ipairs(CHANNEL_CONFIG.DEFAULT_CHANNELS) do
        local channelId = GetChannelName(channelName)
        if channelId and channelId > 0 then ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, channelName) end
    end
    
    -- 事件处理
    ChatBar:UnregisterAllEvents()
    for _, event in ipairs({"PLAYER_ENTERING_WORLD", "ZONE_CHANGED_NEW_AREA", "UPDATE_CHAT_WINDOWS", "CHAT_MSG_WHISPER", "CHAT_MSG_BN_WHISPER"}) do
        ChatBar:RegisterEvent(event)
    end
    
    ChatBar:SetScript("OnEvent", function(self, event)
        if event == "PLAYER_ENTERING_WORLD" or event == "ZONE_CHANGED_NEW_AREA" then
            UpdateBGButtonText()
            C_Timer.After(1, function()
                local config = GetConfig()
                for _, channelName in ipairs(CHANNEL_CONFIG.DEFAULT_CHANNELS) do
                    local channelId = GetChannelName(channelName)
                    -- 只有在频道存在且未被用户屏蔽时才添加
                    if channelId and channelId > 0 and not config.HiddenChannels[channelName] and not IsChannelShown(channelName) then
                        ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, channelName)
                    elseif config.HiddenChannels[channelName] and IsChannelShown(channelName) then
                        -- 如果用户设置了屏蔽但频道仍然显示，则移除它
                        ChatFrame_RemoveChannel(DEFAULT_CHAT_FRAME, channelName)
                    end
                end
                UpdateChannelXIcons()
            end)
        elseif event == "UPDATE_CHAT_WINDOWS" then
            UpdateChannelXIcons()
        elseif event == "CHAT_MSG_WHISPER" or event == "CHAT_MSG_BN_WHISPER" then
            C_Timer.After(0.1, UpdateWhisperButtonFlash)
        end
    end)

    C_Timer.After(1, function() UpdateBGButtonText(); UpdateMinimizeButtonText(); UpdateChannelXIcons() end)
    if not WChat.chatBarInitialized then WChat.chatBarInitialized = true end
end

-- 频道快速切换
local cycles = {
    {"SAY", function() return 1 end},
    {"YELL", function() return 1 end},
    {"PARTY", IsInGroup},
    {"RAID", IsInRaid},
    {"INSTANCE_CHAT", function() return select(2, IsInInstance()) == "pvp" end},
    {"GUILD", IsInGuild},
    {"CHANNEL", function(editbox, currChatType)
        local currNum = currChatType ~= "CHANNEL" and (IsShiftKeyDown() and 21 or 0) or editbox:GetAttribute("channelTarget")
        local h, r, step = IsShiftKeyDown() and {currNum - 1, 1, -1} or {currNum + 1, 20, 1}
        for i = h[1], h[2], h[3] do
            local channelNum, channelName = GetChannelName(i)
            if channelNum and channelNum > 0 and channelName and channelName:find("大脚世界频道") then
                editbox:SetAttribute("channelTarget", i)
                return true
            end
        end
    end},
    {"SAY", function() return 1 end}
}

function ChatEdit_CustomTabPressed(...) return ChatEdit_CustomTabPressed_Inner(...) end

local chatTypeBeforeSwitch, tellTargetBeforeSwitch
function ChatEdit_CustomTabPressed_Inner(self)
    if strsub(tostring(self:GetText()), 1, 1) == "/" then return end
    
    local currChatType = self:GetAttribute("chatType")
    if IsControlKeyDown() then
        if currChatType == "WHISPER" or currChatType == "BN_WHISPER" then
            self:SetAttribute("chatType", chatTypeBeforeSwitch or "SAY")
            ChatEdit_UpdateHeader(self)
            chatTypeBeforeSwitch, tellTargetBeforeSwitch = "WHISPER", self:GetAttribute("tellTarget")
            return
        else
            local newTarget, newTargetType = ChatEdit_GetNextTellTarget()
            if tellTargetBeforeSwitch or (newTarget and newTarget ~= "") then
                self:SetAttribute("chatType", tellTargetBeforeSwitch and chatTypeBeforeSwitch or newTargetType)
                self:SetAttribute("tellTarget", tellTargetBeforeSwitch or newTarget)
                ChatEdit_UpdateHeader(self)
                chatTypeBeforeSwitch, tellTargetBeforeSwitch = currChatType, nil
                return true
            end
        end
    end

    for i, cycle in ipairs(cycles) do
        if cycle[1] == currChatType then
            local h, r, step = IsShiftKeyDown() and {i - 1, 1, -1} or {i + 1, #cycles, 1}
            if currChatType == "CHANNEL" then h[1] = i end
            for j = h[1], h[2], h[3] do
                local cycleData = cycles[j]
                if cycleData and cycleData[2](self, currChatType) then
                    self:SetAttribute("chatType", cycleData[1])
                    ChatEdit_UpdateHeader(self)
                    return
                end
            end
        end
    end
end

-- 聊天表情
local EmoteTableFrame, fmtstring, customEmoteStartIndex = nil, nil, 9
local emotes = (function()
    local result = {}
    -- 团队标记
    for i = 1, 8 do result[i] = {"{rt" .. i .. "}", "Interface\\TargetingFrame\\UI-RaidTargetingIcon_" .. i} end
    -- 自定义表情
    local emoteList = "天使,Angel,生气,Angry,大笑,Biglaugh,鼓掌,Clap,酷,Cool,哭,Cry,可爱,Cutie,鄙视,Despise,美梦,Dreamsmile,尴尬,Embarrass,邪恶,Evil,兴奋,Excited,晕,Faint,打架,Fight,流感,Flu,呆,Freeze,皱眉,Frown,致敬,Greet,鬼脸,Grimace,龇牙,Growl,开心,Happy,心,Heart,恐惧,Horror,生病,Ill,无辜,Innocent,功夫,Kongfu,花痴,Love,邮件,Mail,化妆,Makeup,沉思,Meditate,可怜,Miserable,好,Okay,漂亮,Pretty,吐,Puke,握手,Shake,喊,Shout,闭嘴,Shuuuu,害羞,Shy,睡觉,Sleep,微笑,Smile,吃惊,Suprise,失败,Surrender,流汗,Sweat,流泪,Tear,悲剧,Tears,想,Think,偷笑,Titter,猥琐,Ugly,胜利,Victory,雷锋,Volunteer,委屈,Wronged"
    local idx = 9
    for cn, en in emoteList:gmatch("([^,]+),([^,]+)") do
        result[idx] = {"{" .. cn .. "}", EMOTION_PATH .. "\\" .. en}
        idx = idx + 1
    end
    return result
end)()

-- 表情事件列表
local EMOTE_EVENTS = {"CHAT_MSG_CHANNEL", "CHAT_MSG_SAY", "CHAT_MSG_YELL", "CHAT_MSG_RAID", "CHAT_MSG_RAID_LEADER", "CHAT_MSG_PARTY", "CHAT_MSG_PARTY_LEADER", "CHAT_MSG_GUILD", "CHAT_MSG_AFK", "CHAT_MSG_DND", "CHAT_MSG_INSTANCE_CHAT", "CHAT_MSG_INSTANCE_CHAT_LEADER", "CHAT_MSG_WHISPER", "CHAT_MSG_WHISPER_INFORM", "CHAT_MSG_BN_WHISPER", "CHAT_MSG_BN_WHISPER_INFORM", "CHAT_MSG_COMMUNITIES_CHANNEL"}

local function ChatEmoteFilter(self, event, msg, ...)
    local config = GetConfig()
    if config.EnableEmoteInput then
        for i = customEmoteStartIndex, #emotes do
            if msg:find(emotes[i][1]) then
                msg = msg:gsub(emotes[i][1], format(fmtstring, emotes[i][2]), 1)
            end
        end
    end
    return false, msg, ...
end

local function EmoteIconMouseUp(frame, button)
    if button == "LeftButton" then
        local chatFrame = GetCVar("chatStyle")=="im" and SELECTED_CHAT_FRAME or DEFAULT_CHAT_FRAME
        local eb = chatFrame and chatFrame.editBox
        if eb then eb:Insert(frame.text); eb:Show(); eb:SetFocus() end
    end
    WChat:ToggleEmoteTable()
end

function WChat:InitEmoteTableFrame()
    local config = GetConfig()

    local chatFontSize = floor(select(2, SELECTED_CHAT_FRAME:GetFont()))
    fmtstring = format("\124T%%s:%d\124t", chatFontSize)

    -- 清理旧的表情框架
    if EmoteTableFrame then
        EmoteTableFrame:Hide()
        if EmoteTableFrame.clickCatcher then
            EmoteTableFrame.clickCatcher:Hide()
            EmoteTableFrame.clickCatcher:SetParent(nil)
            EmoteTableFrame.clickCatcher = nil
        end
        EmoteTableFrame:SetParent(nil)
        EmoteTableFrame = nil
    end

    EmoteTableFrame = CreateFrame("Frame", "EmoteTableFrame", UIParent, "BackdropTemplate")

    EmoteTableFrame:SetMovable(true)
    EmoteTableFrame:RegisterForDrag("LeftButton")
    EmoteTableFrame:SetScript("OnDragStart", EmoteTableFrame.StartMoving)
    EmoteTableFrame:SetScript("OnDragStop", EmoteTableFrame.StopMovingOrSizing)
    EmoteTableFrame:EnableMouse(true)



    SetStandardBackdrop(EmoteTableFrame, 0.8, 1, "emote")
    EmoteTableFrame:SetBackdropColor(0.05, 0.05, 0.05, 0.8)
    EmoteTableFrame:SetBackdropBorderColor(0.3, 0.3, 0.3)
    EmoteTableFrame:SetWidth((config.EmoteIconListSize + 6) * 12 + 10)
    EmoteTableFrame:SetHeight((config.EmoteIconListSize + 6) * 5 + 10)

    local function setupClickCatcher(self)
        if not self.clickCatcher then
            self.clickCatcher = CreateFrame("Frame", nil, UIParent)
            self.clickCatcher:SetAllPoints(UIParent)
            self.clickCatcher:SetFrameLevel(self:GetFrameLevel() - 1)
            self.clickCatcher:EnableMouse(true)
            self.clickCatcher:SetScript("OnMouseDown", function()
                if EmoteTableFrame and EmoteTableFrame:IsShown() then EmoteTableFrame:Hide() end
            end)
        end
        self.clickCatcher:Show()
    end

    if WChat.emoteButtonFrame then
        EmoteTableFrame:SetPoint("BOTTOM", WChat.emoteButtonFrame, "TOP", 0, 5)
        EmoteTableFrame:SetScript("OnShow", function(self)
            local panelWidth, screenWidth, left, right = self:GetWidth(), UIParent:GetWidth(), self:GetLeft(), self:GetRight()
            if left and right then
                local offsetX = 0
                if left < 0 then offsetX = -left + 10
                elseif right > screenWidth then offsetX = screenWidth - right - 10 end
                if offsetX ~= 0 then
                    self:ClearAllPoints()
                    self:SetPoint("BOTTOM", WChat.emoteButtonFrame, "TOP", offsetX, 5)
                end
            end
            setupClickCatcher(self)
        end)
    else
        EmoteTableFrame:SetPoint("BOTTOM", ChatFrame1EditBox, "TOP", 0, 30)
        EmoteTableFrame:SetScript("OnShow", setupClickCatcher)
    end


    EmoteTableFrame:SetScript("OnHide", function(self) if self.clickCatcher then self.clickCatcher:Hide() end end)

    EmoteTableFrame:Hide()
    EmoteTableFrame:SetFrameStrata("DIALOG")

    local icon, row, col
    row = 1
    col = 1
    for i = 1, #emotes do
        text = emotes[i][1]
        texture = emotes[i][2]
        icon = CreateFrame("Frame", format("IconButton%d", i), EmoteTableFrame)
        icon:SetWidth(config.EmoteIconListSize + 6)
        icon:SetHeight(config.EmoteIconListSize + 6)
        icon.text = text
        icon.texture = icon:CreateTexture(nil, "ARTWORK")
        icon.texture:SetTexture(texture)
        icon.texture:SetAllPoints(icon)
        icon:Show()
        icon:SetPoint(
            "TOPLEFT",
            5 + (col - 1) * (config.EmoteIconListSize + 6),
            -5 - (row - 1) * (config.EmoteIconListSize + 6)
        )
        icon:SetScript("OnMouseUp", EmoteIconMouseUp)
        icon:EnableMouse(true)
        col = col + 1
        if col > 12 then row = row + 1; col = 1 end
    end

    -- 清理和注册表情事件过滤器
    if self.emoteEventsRegistered then
        for _, event in ipairs(EMOTE_EVENTS) do
            if self.registeredFilters[event] then
                for _, callback in ipairs(self.registeredFilters[event]) do
                    if callback == ChatEmoteFilter then ChatFrame_RemoveMessageEventFilter(event, callback) end
                end
            end
        end
    end
    for _, event in ipairs(EMOTE_EVENTS) do self:RegisterFilter(event, ChatEmoteFilter) end
    self.emoteEventsRegistered = true
end

function WChat:ToggleEmoteTable()
    if not EmoteTableFrame then return end
    if EmoteTableFrame:IsShown() then EmoteTableFrame:Hide() else EmoteTableFrame:Show() end
end

-- 聊天美化
local function SkinChatTabs()
    if _G.ElvUI then
        if not WChat.elvuiWarningShown then WChat.elvuiWarningShown = true end
        return
    end
    
    local inherit = GameFontNormalSmall
    local color = RAID_CLASS_COLORS and RAID_CLASS_COLORS[select(2, UnitClass("player"))] or {r=1,g=0.82,b=0}
    
    local function updateFS(tab, inc, flags, ...)
        local fstring = tab:GetFontString()
        if not fstring then return end
        local font, fontSize = inherit:GetFont()
        fstring:SetFont(font, inc and fontSize + 1 or fontSize, flags)
        if select('#', ...) > 0 then fstring:SetTextColor(...) end
    end
    
    local function OnEnter(self) updateFS(self, nil, "OUTLINE", color.r, color.g, color.b) end
    local function OnLeave(self)
        local id = self:GetID()
        local emphasis = _G["ChatFrame"..id..'TabFlash']:IsShown()
        local r, g, b = (_G["ChatFrame"..id] == SELECTED_CHAT_FRAME) and {color.r, color.g, color.b} or {1, 1, 1}
        updateFS(self, emphasis, nil, r[1], r[2], r[3])
    end
    
    local function faneifyTab(tab)
        if not tab or tab.Fane then return end
        
        -- 隐藏纹理
        local textures = {"leftTexture", "middleTexture", "rightTexture", "leftHighlightTexture", "middleHighlightTexture", "rightHighlightTexture"}
        for _, texture in ipairs(textures) do
            if tab[texture] then tab[texture]:Hide() end
        end
        
        -- 隐藏选中纹理并禁用显示
        local selectedTextures = {"leftSelectedTexture", "middleSelectedTexture", "rightSelectedTexture"}
        for _, texture in ipairs(selectedTextures) do
            if tab[texture] then
                tab[texture]:Hide()
                tab[texture].Show = tab[texture].Hide
            end
        end
        
        tab:HookScript('OnEnter', OnEnter)
        tab:HookScript('OnLeave', OnLeave)
        tab:SetAlpha(1)
        tab.Fane = true
        
        local id = tab:GetID()
        updateFS(tab, nil, nil, id == SELECTED_CHAT_FRAME:GetID() and color.r or 1, id == SELECTED_CHAT_FRAME:GetID() and color.g or 1, id == SELECTED_CHAT_FRAME:GetID() and color.b or 1)
    end
    
    hooksecurefunc('FCFTab_UpdateColors', faneifyTab)
    for i=1, NUM_CHAT_WINDOWS do
        local tab = _G['ChatFrame'..i..'Tab']
        if tab then faneifyTab(tab) end
    end
end

local function CreateChatMinimizeButton()
    if _G.WChat_ChatHider then return end
    local ChatHider = CreateFrame("Frame", "WChat_ChatHider", UIParent)
    ChatHider:SetSize(1,1)
    ChatHider:SetFrameStrata("LOW")
    ChatHider:SetPoint("BOTTOMLEFT", UIParent, 0, 0)
    
    local btn = CreateFrame("Button", "WChat_ChatHideButton", UIParent)
    btn:SetSize(32,36)
    btn:Hide()
    
    local ChatIsHidden = false
    local chatElements = {"GeneralDockManager", "ChatFrameMenuButton", "ChatFrameChannelButton"}
    
    btn:SetScript("OnClick", function()
        ChatIsHidden = not ChatIsHidden
        if ChatIsHidden then
            for i=1, NUM_CHAT_WINDOWS do _G["ChatFrame"..i]:SetParent(ChatHider) end
            for _, element in ipairs(chatElements) do
                if _G[element] then _G[element]:SetParent(ChatHider) end
            end
            ChatHider:Hide()
        else
            ChatHider:Show()
        end
    end)
end

function WChat:InitChatSkin()
    SkinChatTabs()
    CreateChatMinimizeButton()
end

-- 聊天复制
local chatCopyFrame = CreateFrame("Frame", "ChatCopyFrame", UIParent, "BackdropTemplate")
chatCopyFrame:SetPoint("CENTER")
chatCopyFrame:SetSize(700, 400)
chatCopyFrame:Hide()
chatCopyFrame:SetFrameStrata("DIALOG")
chatCopyFrame:SetMovable(true)
chatCopyFrame:EnableMouse(true)
chatCopyFrame:RegisterForDrag("LeftButton")
chatCopyFrame:SetScript("OnDragStart", chatCopyFrame.StartMoving)
chatCopyFrame:SetScript("OnDragStop", chatCopyFrame.StopMovingOrSizing)

-- 设置背景和关闭按钮
chatCopyFrame:SetBackdrop({
    bgFile = "Interface/DialogFrame/UI-DialogBox-Background",
    edgeFile = "Interface/DialogFrame/UI-DialogBox-Border",
    tile = true, tileSize = 16, edgeSize = 16,
    insets = {left = 4, right = 4, top = 4, bottom = 4}
})
chatCopyFrame.close = CreateFrame("Button", nil, chatCopyFrame, "UIPanelCloseButton")
chatCopyFrame.close:SetPoint("TOPRIGHT")

-- 滚动区域和编辑框
local scrollArea = CreateFrame("ScrollFrame", "ChatCopyScrollFrame", chatCopyFrame, "UIPanelScrollFrameTemplate")
scrollArea:SetPoint("TOPLEFT", 10, -30)
scrollArea:SetPoint("BOTTOMRIGHT", -30, 10)

local editBox = CreateFrame("EditBox", nil, chatCopyFrame)
editBox:SetMultiLine(true)
editBox:SetMaxLetters(99999)
editBox:EnableMouse(true)
editBox:SetAutoFocus(false)
editBox:SetFontObject(ChatFontNormal)
editBox:SetWidth(scrollArea:GetWidth())
editBox:SetHeight(270)
editBox:SetScript("OnEscapePressed", function(f) f:GetParent():GetParent():Hide() f:SetText("") end)
scrollArea:SetScrollChild(editBox)

-- 纯文本按钮
local stripBtn = CreateFrame("Button", nil, chatCopyFrame, "UIPanelButtonTemplate")
stripBtn:SetSize(80, 22)
stripBtn:SetText("纯文本")
stripBtn:SetPoint("BOTTOMRIGHT", -10, 10)
stripBtn:SetScript("OnClick", function()
    local str = editBox:GetText()
    -- 清理富文本标记
    local patterns = {
        {"|A.-|a", ""}, {"|T.-|t", ""}, {"|H.-|h(.-)|h", "%1"},
        {"|c%x%x%x%x%x%x%x%x", ""}, {"|r", ""}
    }
    for _, pattern in ipairs(patterns) do
        str = str:gsub(pattern[1], pattern[2])
    end
    editBox:SetText(str)
end)

function WChat:CopyFunc()
    local cf = SELECTED_CHAT_FRAME
    if not cf or cf:GetNumMessages() == 0 then return end
    
    local lines = {}
    for i = 1, cf:GetNumMessages() do
        local msg = cf:GetMessageInfo(i)
        if msg then
            -- 清理富文本标记，保留颜色
            msg = msg:gsub("|H.-|h(.-)|h", "%1"):gsub("|H.-|h", ""):gsub("|h", "")
                     :gsub("|T.-|t", "[表情]"):gsub("|A.-|a", "")
            table.insert(lines, msg)
        end
    end
    
    chatCopyFrame:Show()
    editBox:SetText(table.concat(lines, "\n"))
    editBox:HighlightText(0)
end

function WChat:CopySingleMessage(messageId)
    if not WChat.messageCache or not WChat.messageCache[messageId] then return end
    local cleanMessage = WChat.messageCache[messageId]
        :gsub("|c%x%x%x%x%x%x%x%x", ""):gsub("|r", "")
        :gsub("|H.-|h", ""):gsub("|h", "")
        :gsub("|T.-|t", "[表情]")
    chatCopyFrame:Show()
    editBox:SetText(cleanMessage)
    editBox:HighlightText(0)
end

-- 密语记录
WhisperRecord = {}

local CONFIG = {
    BNET_CLASS_ID = "BN_2",
    AUTO_HIDE_DELAY = 10,
    MAX_LINES = 100,
    COLORS = {
        BNET_FRIEND = {r = 0, g = 1, b = 0.9647, a = 1},
        TITLE = {r = 1, g = 0.843, b = 0, a = 1}
    },
    FRAME = {name = "WChatClassic_WhisperRecord", width = 220, height = 310, itemHeight = 22, maxItems = 12}
}

local dbCache = {globalDB = nil, lastAccess = 0, cacheTimeout = 1}

local function GetWhisperGlobalDB()
    local currentTime = GetTime()
    if dbCache.globalDB and (currentTime - dbCache.lastAccess) < dbCache.cacheTimeout then return dbCache.globalDB end
    
    if not WChatClassicWhisperDB then
        WChatClassicWhisperDB = {Open = true, Tips = true, SoundAlert = true, Days = 30, record = {{}, {}}}
    end
    if WChatClassicWhisperDB.SoundAlert == nil then WChatClassicWhisperDB.SoundAlert = true end
    
    dbCache.globalDB = WChatClassicWhisperDB
    dbCache.lastAccess = currentTime
    return WChatClassicWhisperDB
end

local function GetAllWhisperRecords() return GetWhisperGlobalDB().record[1] or {} end
local function ClearDBCache() dbCache.globalDB, dbCache.lastAccess = nil, 0 end



-- 工具函数
local function GetClassRGB()
    if _G.WanTinyUI and _G.WanTinyUI.GetClassRGB then
        local rgb = _G.WanTinyUI.GetClassRGB()
        return rgb[1], rgb[2], rgb[3]
    else
        local color = RAID_CLASS_COLORS and RAID_CLASS_COLORS[select(2, UnitClass("player"))] or {r=1,g=0.82,b=0}
        return color.r, color.g, color.b
    end
end

local function SetWanTinyFont(fontString, size)
    fontString:SetFont(STANDARD_TEXT_FONT, size or 14, "OUTLINE")
end

local function SetWanTinyBackdrop(frame, bgAlpha, borderAlpha)
    if not frame.SetBackdrop then
        Mixin(frame, BackdropTemplateMixin)
        frame:OnBackdropLoaded()
    end
    local r, g, b = GetClassRGB()
    frame:SetBackdrop({bgFile = "Interface/ChatFrame/ChatFrameBackground", edgeFile = "Interface/ChatFrame/ChatFrameBackground", edgeSize = 1})
    frame:SetBackdropColor(0, 0, 0, bgAlpha or 0.1)
    frame:SetBackdropBorderColor(r, g, b, borderAlpha or 0.5)
end

local WhisperUtils = {
    CLASS_COLORS = CLASS_COLORS,
    CLASS_ICON_TCOORDS = CLASS_ICON_COORDS,
    GetClassRGB = GetClassRGB,
    SetWanTinyFont = SetWanTinyFont,
    SetWanTinyBackdrop = SetWanTinyBackdrop,
    CreateTitle = function(parent, text, font, yOffset)
        local title = parent:CreateFontString(nil, "OVERLAY", font or "GameFontNormal")
        title:SetPoint("TOP", 0, yOffset or -4)
        title:SetText(text or "")
        title:SetTextColor(CONFIG.COLORS.TITLE.r, CONFIG.COLORS.TITLE.g, CONFIG.COLORS.TITLE.b, CONFIG.COLORS.TITLE.a)
        SetWanTinyFont(title, 16)
        return title
    end,
    CreateCloseButton = function(parent, onClick)
        local btn = CreateFrame("Button", nil, parent, "UIPanelCloseButton")
        btn:SetPoint("TOPRIGHT", -2, -2)
        btn:SetSize(24, 24)
        if onClick then btn:SetScript("OnClick", onClick) end
        return btn
    end,
    CreateMessageFrame = function(parent, width, height)
        local msgFrame = CreateFrame("ScrollingMessageFrame", nil, parent)
        msgFrame:SetSize(width or 280, height or 150)
        msgFrame:SetFont(STANDARD_TEXT_FONT, 13, "OUTLINE")
        msgFrame:SetJustifyH("LEFT")
        msgFrame:SetMaxLines(CONFIG.MAX_LINES)
        msgFrame:SetFading(false)
        msgFrame:SetHyperlinksEnabled(true)
        msgFrame:EnableMouseWheel(true)
        msgFrame:SetScript("OnMouseWheel", function(self, delta)
            if delta > 0 then self:ScrollUp() else self:ScrollDown() end
        end)
        return msgFrame
    end,
    SetFrameBackdrop = function(frame, bgAlpha) SetWanTinyBackdrop(frame, bgAlpha, 0.5) end,
    CreateMovableFrame = function(name, parent, width, height)
        local frame = CreateFrame("Frame", name, parent, "BackdropTemplate")
        frame:SetSize(width, height)
        frame:SetMovable(true)
        frame:EnableMouse(true)
        frame:RegisterForDrag("LeftButton")
        frame:SetScript("OnDragStart", frame.StartMoving)
        frame:SetScript("OnDragStop", frame.StopMovingOrSizing)
        return frame
    end,
    CreateButton = function(parent, text, width, height)
        local r, g, b = GetClassRGB()
        local button = CreateFrame("Button", nil, parent, "BackdropTemplate")
        button:SetSize(width or 80, height or 22)
        SetWanTinyBackdrop(button, 0.07, 0.5)
        
        local fontString = button:CreateFontString(nil, "OVERLAY")
        fontString:SetAllPoints()
        SetWanTinyFont(fontString, 14)
        fontString:SetText(text or "")
        fontString:SetTextColor(1, 1, 1, 1)
        
        button:SetScript("OnEnter", function(self)
            self:SetBackdropColor(r, g, b, 0.28)
            self:SetBackdropBorderColor(r, g, b, 1)
        end)
        button:SetScript("OnLeave", function(self)
            self:SetBackdropColor(0, 0, 0, 0.07)
            self:SetBackdropBorderColor(r, g, b, 0.5)
        end)
        return button
    end,
    CreateCheckbox = function(parent, text, dbKey)
        local checkbox = CreateFrame("CheckButton", nil, parent, "UICheckButtonTemplate")
        checkbox:SetSize(20, 20)
        checkbox.text = checkbox:CreateFontString(nil, "OVERLAY", "GameFontNormal")
        checkbox.text:SetPoint("LEFT", checkbox, "RIGHT", 5, 0)
        checkbox.text:SetText(text or "")
        if dbKey then
            local db = GetWhisperGlobalDB()
            checkbox:SetChecked(db[dbKey])
            checkbox:SetScript("OnClick", function(self) db[dbKey] = self:GetChecked() end)
        end
        return checkbox
    end
}
WhisperRecord.Utils = WhisperUtils

-- 将WhisperUtils的函数复制到Utils中，以保持向后兼容
for k, v in pairs(WhisperUtils) do
    if type(v) == "function" then Utils[k] = v end
end

local function SetAutoHideTimer(chatContent, delay)
    if chatContent.hideTimer then chatContent.hideTimer:Cancel() end
    chatContent.hideTimer = C_Timer.NewTimer(delay or CONFIG.AUTO_HIDE_DELAY, function()
        if chatContent:IsShown() then
            chatContent:Hide()
            WhisperRecord.ClearSelectedState()
        end
    end)
end

local function NotifyWhisperButtonUpdate()
    C_Timer.After(0.1, UpdateWhisperButtonFlash)
end

-- 简化的类信息处理
WhisperUtils.GetPlayerClassColor = function(class)
    return WhisperUtils.CLASS_COLORS[class] or WhisperUtils.CLASS_COLORS["PRIEST"]
end

WhisperUtils.GetClassIcon = function(class)
    if class == "BN_2" then
        return "interface/friendsframe/battlenet-portrait.blp", {0, 1, 0, 1}
    elseif class and WhisperUtils.CLASS_ICON_TCOORDS[class] then
        return "Interface/TargetingFrame/UI-Classes-Circles", WhisperUtils.CLASS_ICON_TCOORDS[class]
    end
    return nil, nil
end

local ClassInfo = {
    playerInfoCache = {},
    queue = {},
    Cache = function(self, playerName, class) if playerName and class then self.playerInfoCache[playerName] = class end end,
    Get = function(self, playerName)
        if not playerName then return nil end
        if self.playerInfoCache[playerName] then return self.playerInfoCache[playerName] end
        
        for _, unit in ipairs({"target", "mouseover"}) do
            local _, class = UnitClass(unit)
            if class and UnitName(unit) == playerName then
                self:Cache(playerName, class)
                return class
            end
        end
        return nil
    end,
    AddRetry = function(self, playerName, callback)
        if not callback then return end
        for _, item in ipairs(self.queue) do if item.playerName == playerName then return end end
        table.insert(self.queue, {playerName = playerName, callback = callback, retries = 0})
    end,
    Process = function(self)
        if #self.queue == 0 then return end
        local item = table.remove(self.queue, 1)
        if item.retries < 3 then
            local class = self:Get(item.playerName)
            if class then
                item.callback(class)
            else
                item.retries = item.retries + 1
                table.insert(self.queue, item)
            end
        end
    end
}
WhisperRecord.Utils.ClassInfo = ClassInfo

-- 获取玩家职业信息
local function GetPlayerClassInfo(playerName, guid, isFromBNet, bnSenderID)
    local playerClass, finalPlayerName = nil, playerName

    if isFromBNet then
        playerClass = CONFIG.BNET_CLASS_ID
        if bnSenderID and type(bnSenderID) == "number" and bnSenderID > 0 then
            local success, accountInfo = pcall(C_BattleNet.GetAccountInfoByID, bnSenderID)
            if success and accountInfo then finalPlayerName = accountInfo.accountName or playerName end
        end
    else
        -- 普通玩家处理
        if guid and string.find(guid, "Player-") then
            local _, class = GetPlayerInfoByGUID(guid)
            if class then playerClass = class end
        end
        
        -- 确保玩家名包含服务器
        local _, realm = strsplit("-", playerName)
        finalPlayerName = realm and playerName or (playerName .. "-" .. GetRealmName())

        -- 缓存和重试机制
        if playerClass then
            WhisperRecord.Utils.ClassInfo:Cache(finalPlayerName, playerClass)
        else
            playerClass = WhisperRecord.Utils.ClassInfo:Get(finalPlayerName)
            if not playerClass then
                WhisperRecord.Utils.ClassInfo:AddRetry(finalPlayerName, function(name, class)
                    WhisperRecord.UpdatePlayerClass(name, class)
                end)
            end
        end
    end

    return finalPlayerName, playerClass
end

local function RecordWhisperInternal(playerName, message, isIncoming, isFromBNet, playerClass, showTip)
    if not GetWhisperGlobalDB().Open then return end

    local finalPlayerName = WhisperRecord.SaveMessage(playerName, {
        time = time(),
        message = message,
        incoming = isIncoming,
        isFromBNet = isFromBNet,
        class = playerClass
    }, playerClass, isIncoming)

    WhisperRecord.RefreshUI(finalPlayerName)

    if isIncoming and GetWhisperGlobalDB().SoundAlert then
        PlaySoundFile(TEXTURE_PATH .. "\\Sounds\\Notify.ogg", "Master")
    end
end

function WhisperRecord.RecordWhisperMessage(event, ...)
    local message, sender, guid, bnSenderID = select(1, ...), select(2, ...), select(12, ...), select(13, ...)
    if not message or not sender then return end
    local isFromBNet = (event == "CHAT_MSG_BN_WHISPER")
    local playerName, playerClass = GetPlayerClassInfo(sender, guid, isFromBNet, bnSenderID)
    RecordWhisperInternal(playerName, message, true, isFromBNet, playerClass, true)
end

function WhisperRecord.RecordSentWhisper(message, target, guid, isBNet)
    if not message or not target then return end
    local playerName, playerClass = GetPlayerClassInfo(target, guid, isBNet or false, nil)
    RecordWhisperInternal(playerName, message, false, isBNet or false, playerClass, false)
end

function WhisperRecord.SaveMessage(playerName, messageData, playerClass, hasUnread)
    if hasUnread == nil then hasUnread = messageData.incoming end
    local dateKey = date("%Y%m%d", messageData.time)
    local db = GetWhisperGlobalDB()
    if not db.record[1][playerName] then
        db.record[1][playerName] = {class = playerClass, lastTime = messageData.time, messages = {}, hasUnread = hasUnread}
    end
    if not db.record[1][playerName].messages[dateKey] then
        db.record[1][playerName].messages[dateKey] = {}
    end
    table.insert(db.record[1][playerName].messages[dateKey], messageData)
    db.record[1][playerName].lastTime = messageData.time
    db.record[1][playerName].class = playerClass or db.record[1][playerName].class
    if hasUnread then
        db.record[1][playerName].hasUnread = true
        NotifyWhisperButtonUpdate()
    end
    ClearDBCache()
    return playerName
end

function WhisperRecord.UpdatePlayerClass(playerName, class)
    local globalDB = GetWhisperGlobalDB()
    if globalDB.record[1][playerName] then
        globalDB.record[1][playerName].class = class
        WhisperRecord.UpdateWhisperList()
    end
end

function WhisperRecord.RefreshUI(playerName)
    WhisperRecord.UpdateWhisperList()
end

function WhisperRecord.CleanOldRecords()
    local globalDB = GetWhisperGlobalDB()
    local cutoffTime = time() - (globalDB.Days * CONFIG.TIME.HOURS_PER_DAY * CONFIG.TIME.MINUTES_PER_HOUR * CONFIG.TIME.SECONDS_PER_MINUTE)

    for playerName, playerData in pairs(globalDB.record[1]) do
        if playerData.lastTime < cutoffTime then
            globalDB.record[1][playerName] = nil
        end
    end
    ClearDBCache()
end

function WhisperRecord.HasUnreadMessages()
    local records = GetAllWhisperRecords()
    for _, playerData in pairs(records) do if playerData.hasUnread then return true end end
    return false
end

-- 创建主框架
local function CreateMainFrame()
    local config = CONFIG.MAIN_FRAME
    local WanMY = Utils.CreateMovableFrame(config.name, UIParent, config.width, config.height)
    WanMY:SetPoint("CENTER", UIParent, "CENTER", 0, 70)
    WanMY:SetClampedToScreen(true)
    Utils.SetFrameBackdrop(WanMY, CONFIG.UI.BACKDROP_ALPHA)
    WanMY:Hide()

    -- 让ESC优先关闭本框体
    WanMY:SetFrameStrata("DIALOG")
    WanMY:SetToplevel(true)
    WanMY:SetPropagateKeyboardInput(false)
    table.insert(UISpecialFrames, config.name)

    WanMY.biaoti = Utils.CreateTitle(WanMY, "密语记录")
    WanMY.closeButton = Utils.CreateCloseButton(WanMY)

    return WanMY
end

local function CreateSettingsButton(WanMY)
    local shezhi = Utils.CreateButton(WanMY, "", 18, 18)
    shezhi:SetPoint("TOPLEFT", WanMY, "TOPLEFT", 4, -1.8)
    shezhi:SetHighlightTexture("Interface/Buttons/UI-Common-MouseHilight")
    shezhi.Tex = shezhi:CreateTexture(nil, "OVERLAY")
    shezhi.Tex:SetTexture("Interface/GossipFrame/BinderGossipIcon")
    shezhi.Tex:SetPoint("CENTER", 0, 0)
    shezhi.Tex:SetSize(16, 16)
    shezhi:SetScript("OnMouseDown", function(self) self.Tex:SetPoint("CENTER", -1, -1) end)
    shezhi:SetScript("OnMouseUp", function(self) self.Tex:SetPoint("CENTER", 0, 0) end)
    return shezhi
end

local function CreateSettingsPanel(WanMY)
    local config = CONFIG.MAIN_FRAME
    local shezhiF = CreateFrame("Frame", nil, WanMY.shezhi, "BackdropTemplate")
    shezhiF:SetSize(config.width, config.height)
    shezhiF:SetPoint("TOPRIGHT", WanMY, "TOPLEFT", -1, 0)
    Utils.SetFrameBackdrop(shezhiF, CONFIG.UI.SETTINGS_BACKDROP_ALPHA)
    shezhiF:Hide()

    shezhiF.closeButton = Utils.CreateCloseButton(shezhiF)
    shezhiF.biaoti = Utils.CreateTitle(shezhiF, "设置")

    return shezhiF
end

local function CreateSettingsOptions(shezhiF)
    local settingsOptions = {{key = "Open", text = "启用密语记录", y = -30}, {key = "Tips", text = "新密语提醒", y = -60}, {key = "SoundAlert", text = "声音提醒", y = -90}}
    for _, option in ipairs(settingsOptions) do
        local checkbox = Utils.CreateCheckbox(shezhiF, option.text, option.key)
        checkbox:SetPoint("TOPLEFT", shezhiF, "TOPLEFT", 10, option.y)
        shezhiF[option.key:lower()] = checkbox
    end
    return settingsOptions
end

local function CreateDaysSettings(shezhiF)
    shezhiF.tianshulabel = shezhiF:CreateFontString(nil, "OVERLAY", "GameFontNormal")
    shezhiF.tianshulabel:SetPoint("TOPLEFT", shezhiF, "TOPLEFT", 10, -150)
    shezhiF.tianshulabel:SetText("保存天数:")
    shezhiF.tianshu = CreateFrame("EditBox", nil, shezhiF)
    shezhiF.tianshu:SetSize(50, 20)
    shezhiF.tianshu:SetPoint("LEFT", shezhiF.tianshulabel, "RIGHT", 5, 0)
    shezhiF.tianshu:SetAutoFocus(false)
    shezhiF.tianshu:SetText(tostring(GetWhisperGlobalDB()["Days"]))

    -- 使用与WantinyUI相同的样式
    local leftTexture = shezhiF.tianshu:CreateTexture(nil, "BACKGROUND")
    leftTexture:SetTexture("interface/common/commonsearch")
    leftTexture:SetSize(8, 20)
    leftTexture:SetPoint("LEFT", shezhiF.tianshu, "LEFT", -5, 0)
    leftTexture:SetTexCoord(0.88, 0.95, 0.01, 0.31)

    local rightTexture = shezhiF.tianshu:CreateTexture(nil, "BACKGROUND")
    rightTexture:SetTexture("interface/common/commonsearch")
    rightTexture:SetSize(8, 20)
    rightTexture:SetPoint("RIGHT", shezhiF.tianshu, "RIGHT", 0, 0)
    rightTexture:SetTexCoord(0, 0.07, 0.338, 0.638)

    local middleTexture = shezhiF.tianshu:CreateTexture(nil, "BACKGROUND")
    middleTexture:SetTexture("interface/common/commonsearch")
    middleTexture:SetPoint("LEFT", leftTexture, "RIGHT")
    middleTexture:SetPoint("RIGHT", rightTexture, "LEFT")
    middleTexture:SetHeight(20)
    middleTexture:SetTexCoord(0, 0.8, 0.01, 0.31)

    shezhiF.tianshu:SetFontObject(ChatFontNormal)

    shezhiF.tianshu:SetScript("OnTabPressed", function(self) self:ClearFocus() end)
    shezhiF.tianshu:SetScript("OnEscapePressed", function(self) self:ClearFocus() end)
    shezhiF.tianshu:SetScript("OnEditFocusLost", function(self) self:HighlightText(0, 0) end)
    shezhiF.tianshu:SetScript("OnEditFocusGained", function(self) self:HighlightText() end)
    shezhiF.tianshu:SetScript("OnEnterPressed", function(self)
        local days = math.max(1, math.min(365, tonumber(self:GetText()) or 7))
        GetWhisperGlobalDB()["Days"] = days
        self:SetText(tostring(days))
        self:ClearFocus()
    end)
end

local function CreateClearButton(shezhiF)
    shezhiF.MIYUJILUBUT = Utils.CreateButton(shezhiF, "清空记录", 76, 20)
    shezhiF.MIYUJILUBUT:SetPoint("BOTTOM", shezhiF, "BOTTOM", 0, 10)  -- 改为底部居中
    shezhiF.MIYUJILUBUT:SetScript("OnClick", function() StaticPopup_Show("CHONGZHI_MIYUJILU") end)
end

local function SetupSettingsPanelEvents(shezhiF, settingsOptions)
    shezhiF:SetScript("OnShow", function(self)
        local db = GetWhisperGlobalDB()
        for _, option in ipairs(settingsOptions) do
            if self[option.key:lower()] then
                self[option.key:lower()]:SetChecked(db[option.key])
            end
        end
        self.tianshu:SetText(tostring(db["Days"]))
    end)
end

function WhisperRecord.CreateWhisperRecordFrame()
    local WanMY = CreateMainFrame()
    WanMY.shezhi = CreateSettingsButton(WanMY)
    WanMY.shezhiF = CreateSettingsPanel(WanMY)
    local settingsOptions = CreateSettingsOptions(WanMY.shezhiF)
    CreateDaysSettings(WanMY.shezhiF)
    CreateClearButton(WanMY.shezhiF)
    SetupSettingsPanelEvents(WanMY.shezhiF, settingsOptions)
    WanMY.shezhi:SetScript("OnClick", function() WanMY.shezhiF:SetShown(not WanMY.shezhiF:IsShown()) end)
    WhisperRecord.CreateWhisperListArea(WanMY)
    WhisperRecord.WanMY = WanMY
    return WanMY
end

function WhisperRecord.CreateWhisperListArea(WanMY)
    local config = CONFIG.MAIN_FRAME
    WanMY.nr = CreateFrame("Frame", nil, WanMY)
    WanMY.nr:SetSize(config.width - 10, config.height - 50)
    WanMY.nr:SetPoint("TOPLEFT", WanMY, "TOPLEFT", 5, -25)
    WanMY.titleDivider = WanMY:CreateTexture(nil, "ARTWORK")
    WanMY.titleDivider:SetColorTexture(CONFIG.COLORS.DIVIDER.r, CONFIG.COLORS.DIVIDER.g, CONFIG.COLORS.DIVIDER.b, CONFIG.COLORS.DIVIDER.a)
    WanMY.titleDivider:SetHeight(2)
    WanMY.titleDivider:SetPoint("TOPLEFT", WanMY, "TOPLEFT", 8, -22)
    WanMY.titleDivider:SetPoint("TOPRIGHT", WanMY, "TOPRIGHT", -8, -22)
    WanMY.chatContent = CreateFrame("Frame", nil, WanMY, "BackdropTemplate")
    WanMY.chatContent:SetSize(300, 200)
    WanMY.chatContent:SetPoint("TOPLEFT", WanMY, "TOPRIGHT", 5, 0)
    Utils.SetFrameBackdrop(WanMY.chatContent, CONFIG.UI.CHAT_CONTENT_BACKDROP_ALPHA)
    WanMY.chatContent:Hide()
    WanMY.chatContent.title = Utils.CreateTitle(WanMY.chatContent, "聊天记录", "GameFontNormal", -8)
    WanMY.chatContent.closeBtn = Utils.CreateCloseButton(WanMY.chatContent, function()
        WanMY.chatContent:Hide()
        WhisperRecord.ClearSelectedState()
    end)
    WanMY.chatContent.messageFrame = Utils.CreateMessageFrame(WanMY.chatContent, 280, 150)
    WanMY.chatContent.messageFrame:SetPoint("TOP", WanMY.chatContent, "TOP", 0, -30)
    WanMY.chatContent.hideTimer = nil
    WanMY.chatContent:SetScript("OnEnter", function(self)
        if self.hideTimer then self.hideTimer:Cancel(); self.hideTimer = nil end
    end)
    WanMY.chatContent:SetScript("OnLeave", function(self)
        if self.hideTimer then self.hideTimer:Cancel() end
        self.hideTimer = C_Timer.NewTimer(1, function()
            if self:IsShown() and not self:IsMouseOver() then
                self:Hide()
                WhisperRecord.ClearSelectedState()
            end
        end)
    end)
    WanMY.nr.Scroll = CreateFrame("ScrollFrame", nil, WanMY.nr, "UIPanelScrollFrameTemplate")
    WanMY.nr.Scroll:SetSize(config.width - 10, config.height - 50)
    WanMY.nr.Scroll:SetPoint("TOPRIGHT", WanMY.nr, "TOPRIGHT", 0, 0)
    WanMY.nr.Scroll:SetPoint("TOPLEFT", WanMY.nr, "TOPLEFT", 0, 0)
    if WanMY.nr.Scroll.ScrollBar then
        WanMY.nr.Scroll.ScrollBar:SetScale(CONFIG.UI.SCROLLBAR_SCALE)
        WanMY.nr.Scroll.ScrollBar:ClearAllPoints()
        WanMY.nr.Scroll.ScrollBar:SetPoint("TOPRIGHT", WanMY.nr.Scroll, "TOPRIGHT", 0, -18)
        WanMY.nr.Scroll.ScrollBar:SetPoint("BOTTOMRIGHT", WanMY.nr.Scroll, "BOTTOMRIGHT", 0, -10)
    end
    WanMY.nr.Scroll.child = CreateFrame("Frame", nil, WanMY.nr.Scroll)
    WanMY.nr.Scroll.child:SetSize(config.width - 30, config.itemHeight * config.maxItems)
    WanMY.nr.Scroll:SetScrollChild(WanMY.nr.Scroll.child)
    WanMY.nr.Scroll.child.list = {}
    for i = 1, config.maxItems do
        local listItem = CreateFrame("Button", nil, WanMY.nr.Scroll.child)
        listItem:SetSize(config.width - 30, config.itemHeight)
        listItem:SetPoint("TOPLEFT", WanMY.nr.Scroll.child, "TOPLEFT", 0, -(i-1) * config.itemHeight)

        -- 确保按钮可以接收鼠标事件
        listItem:EnableMouse(true)
        listItem:RegisterForClicks("LeftButtonUp")

        -- 按照源代码方式创建纹理
        for _, data in ipairs({
            {"bg", "BACKGROUND", "Interface/Buttons/UI-Listbox-Highlight2", CONFIG.UI.LIST_ITEM_BG_ALPHA},
            {"highlight", "HIGHLIGHT", "Interface/Buttons/UI-Listbox-Highlight", CONFIG.UI.LIST_ITEM_HIGHLIGHT_ALPHA}
        }) do
            local name, layer, texture, alpha = unpack(data)
            listItem[name] = listItem:CreateTexture(nil, layer)
            listItem[name]:SetAllPoints()
            listItem[name]:SetTexture(texture)
            listItem[name]:SetAlpha(alpha)
        end
        listItem.classIcon = listItem:CreateTexture(nil, "BORDER")
        listItem.classIcon:SetPoint("LEFT", listItem, "LEFT", 4, 0)
        listItem.classIcon:SetSize(config.itemHeight - 5, config.itemHeight - 5)
        listItem.name = listItem:CreateFontString(nil, "OVERLAY", "GameFontNormal")  -- 使用GameFontNormal而不是Small
        listItem.name:SetPoint("LEFT", listItem.classIcon, "RIGHT", 4, 0)
        listItem.name:SetJustifyH("LEFT")
        listItem.deleteBtn = CreateFrame("Button", nil, listItem)
        listItem.deleteBtn:SetSize(16, 16)
        listItem.deleteBtn:SetPoint("RIGHT", listItem, "RIGHT", 10, 0)
        listItem.deleteBtn:SetNormalTexture("Interface/Buttons/UI-Panel-MinimizeButton-Up")
        listItem.deleteBtn:SetHighlightTexture("Interface/Buttons/UI-Panel-MinimizeButton-Highlight")
        listItem.deleteBtn:SetPushedTexture("Interface/Buttons/UI-Panel-MinimizeButton-Down")
        listItem.deleteBtn:SetScript("OnClick", function(self)
            local playerName = self:GetParent().playerName
            if playerName then WhisperRecord.DeletePlayerRecord(playerName) end
        end)
        listItem.deleteBtn:SetScript("OnEnter", function(self)
            GameTooltip:SetOwner(self, "ANCHOR_RIGHT")
            GameTooltip:SetText("删除此玩家的记录", 1, 1, 1)
            GameTooltip:Show()
        end)
        listItem.deleteBtn:SetScript("OnLeave", function() GameTooltip:Hide() end)
        -- 恢复OnEnter展开记录功能（悬浮展开聊天记录）
        listItem:SetScript("OnEnter", function(self)
            if self.playerName then
                WhisperRecord.ShowChatContent(self.playerName)
                WhisperRecord.ClearSelectedState()
                self.selected = true
                local globalDB = GetWhisperGlobalDB()
                if globalDB and globalDB.record and globalDB.record[1] and globalDB.record[1][self.playerName] then
                    globalDB.record[1][self.playerName].hasUnread = false
                    WhisperRecord.UpdateWhisperList()
                    NotifyWhisperButtonUpdate()
                end
            end
        end)

        -- 添加点击事件处理（整行点击进入密语对话）
        listItem:SetScript("OnClick", function(self)
            if self.playerName then
                local name = GetDisplayName(self.playerName, true)  -- 使用完整名称进行密语
                if name then
                    -- 检查是否为战网好友（使用与名字点击相同的逻辑）
                    local globalDB = GetWhisperGlobalDB()
                    local playerData = globalDB and globalDB.record and globalDB.record[1] and globalDB.record[1][self.playerName]
                    if playerData and playerData.class == CONFIG.BNET_CLASS_ID then
                        -- 战网好友：使用战网密语
                        local displayName = GetDisplayName(self.playerName)
                        ChatFrame_SendBNetTell(displayName)
                    else
                        -- 普通玩家：使用标准密语
                        ChatFrame_SendTell(name)
                    end
                end
            end
        end)
        listItem:SetScript("OnLeave", function(self)
            local WanMY = WhisperRecord.WanMY
            if WanMY and WanMY.chatContent:IsShown() then
                if WanMY.chatContent.hideTimer then WanMY.chatContent.hideTimer:Cancel() end
                WanMY.chatContent.hideTimer = C_Timer.NewTimer(0.15, function()
                    if WanMY.chatContent:IsShown() and not WanMY.chatContent:IsMouseOver() then
                        WanMY.chatContent:Hide()
                        WhisperRecord.ClearSelectedState()
                    end
                end)
            end
        end)
        listItem:SetScript("OnMouseUp", function(self, button)
            if not self.playerName then return end
            local x, y = GetCursorPosition()
            local scale = self:GetEffectiveScale()
            x, y = x / scale, y / scale
            local left, bottom, width, height = self.name:GetRect()
            if button == "LeftButton" and left and bottom and width and height then
                if x >= left and x <= left + width and y >= bottom and y <= bottom + height then
                    local name = GetDisplayName(self.playerName, true)  -- 使用完整名称进行密语
                    if name then
                    -- 检查是否为战网好友
                    local globalDB = GetWhisperGlobalDB()
                    local playerData = globalDB and globalDB.record and globalDB.record[1] and globalDB.record[1][self.playerName]
                    if playerData and playerData.class == CONFIG.BNET_CLASS_ID then
                        local displayName = GetDisplayName(self.playerName)
                        ChatFrame_SendBNetTell(displayName)
                    else
                        ChatFrame_SendTell(name)
                    end
                    return
                end
                end
            elseif button == "RightButton" then
                -- 使用原生菜单，避免安全错误
                local name = GetDisplayName(self.playerName, true)  -- 使用完整名称进行菜单操作
                if name and FriendsFrame_ShowDropdown then
                    FriendsFrame_ShowDropdown(name, 1)
                end
            end
        end)
        WanMY.nr.Scroll.child.list[i] = listItem
    end
end

function WhisperRecord.ClearSelectedState()
    local WanMY = WhisperRecord.WanMY
    if not WanMY then return end
    for _, item in pairs(WanMY.nr.Scroll.child.list) do item.selected = false end
end

function WhisperRecord.ShowChatContent(playerName)
    if not playerName then return end
    local WanMY = WhisperRecord.WanMY
    if not WanMY then return end
    local chatContent = WanMY.chatContent
    chatContent.currentPlayer = playerName
    WhisperRecord.ClearSelectedState()
    local globalDB = GetWhisperGlobalDB()
    local playerData = globalDB and globalDB.record and globalDB.record[1] and globalDB.record[1][playerName]
    if playerData then
        local classColor = WhisperRecord.Utils.GetPlayerClassColor(playerData.class)
        chatContent.title:SetText("与 " .. playerName .. " 的聊天记录")
        chatContent.title:SetTextColor(classColor.r, classColor.g, classColor.b, 1)
    else
        chatContent.title:SetText("与 " .. playerName .. " 的聊天记录")
        chatContent.title:SetTextColor(1, 1, 1, 1)
    end
    chatContent:ClearAllPoints()
    if (select(1, WanMY:GetCenter()) or 0) < UIParent:GetWidth() / 2 then
        chatContent:SetPoint("TOPLEFT", WanMY, "TOPRIGHT", 5, 0)
    else
        chatContent:SetPoint("TOPRIGHT", WanMY, "TOPLEFT", -5, 0)
    end
    chatContent.messageFrame:Clear()
    WhisperRecord.LoadRecentMessages(playerName)
    chatContent:Show()
    SetAutoHideTimer(chatContent)
end

function WhisperRecord.LoadRecentMessages(playerName)
    local WanMY = WhisperRecord.WanMY
    if not WanMY then return end
    local globalDB = GetWhisperGlobalDB()
    local playerData = globalDB and globalDB.record and globalDB.record[1] and globalDB.record[1][playerName]
    if not playerData or not playerData.messages then return end
    local allMessages = {}
    for dateKey, messages in pairs(playerData.messages) do
        for _, msgData in ipairs(messages) do
            table.insert(allMessages, msgData)
        end
    end
    table.sort(allMessages, function(a, b) return a.time < b.time end)
    local startIndex = math.max(1, #allMessages - 20 + 1)
    for i = startIndex, #allMessages do
        WhisperRecord.Utils.AddFormattedMessage(WanMY.chatContent.messageFrame, allMessages[i], {history=false})
    end
    WanMY.chatContent.messageFrame:ScrollToBottom()
end

function WhisperRecord.DeletePlayerRecord(playerName)
    if not playerName then return end
    StaticPopup_Show("DELETE_WHISPER_RECORD", playerName, nil, playerName)
end

local function HasActualMessages(playerName)
    local globalDB = GetWhisperGlobalDB()
    local playerData = globalDB.record[1][playerName]
    if not playerData or not playerData.messages then return false end
    for _, messages in pairs(playerData.messages) do if #messages > 0 then return true end end
    return false
end
WhisperRecord.Utils.HasActualMessages = HasActualMessages

function WhisperRecord.UpdateWhisperList()
    local WanMY = WhisperRecord.WanMY
    if not WanMY or not WanMY:IsShown() then return end
    local allRecords = GetAllWhisperRecords()
    local playerList = {}
    for playerName, data in pairs(allRecords) do
        if data.lastTime then
            table.insert(playerList, {
                name = playerName,
                lastTime = data.lastTime,
                class = data.class,
                hasUnread = data.hasUnread or false,
                hasMessages = HasActualMessages(playerName)
            })
        end
    end
    table.sort(playerList, function(a, b)
        return a.lastTime > b.lastTime
    end)

    for i, listItem in ipairs(WanMY.nr.Scroll.child.list) do
        if i <= #playerList then
            local playerInfo = playerList[i]
            listItem.playerName = playerInfo.name

            -- 检查是否为跨服玩家，如果是则显示服务器名称
            local currentRealm = GetRealmName()
            local playerName, playerRealm = strsplit("-", playerInfo.name)
            local shouldShowRealm = playerRealm and playerRealm ~= currentRealm

            listItem.name:SetText(GetDisplayName(playerInfo.name, shouldShowRealm))

            -- 设置职业图标
            local iconTexture, iconCoords = WhisperRecord.Utils.GetClassIcon(playerInfo.class)
            if iconTexture and iconCoords then
                listItem.classIcon:SetTexture(iconTexture)
                listItem.classIcon:SetTexCoord(unpack(iconCoords))
                listItem.classIcon:Show()
            else
                listItem.classIcon:Hide()
            end

            -- 设置名字颜色
            local classColor = WhisperRecord.Utils.GetPlayerClassColor(playerInfo.class)
            listItem.name:SetTextColor(classColor.r, classColor.g, classColor.b, 1)

            -- 创建未读背景（按照源代码标准）
            if not listItem.unreadBg then
                listItem.unreadBg = listItem:CreateTexture(nil, "BACKGROUND")
                listItem.unreadBg:SetAllPoints(listItem)
                listItem.unreadBg:SetColorTexture(0, 1, 0, 0.2)  -- 绿色背景，透明度0.2
            end

            if playerInfo.hasUnread then listItem.unreadBg:Show() else listItem.unreadBg:Hide() end

            if playerInfo.hasMessages then listItem.deleteBtn:Show() else listItem.deleteBtn:Hide() end

            listItem:Show()
        else
            listItem:Hide()
            listItem.playerName = nil
        end
    end
end



-- 静态弹窗对话框
StaticPopupDialogs["CHONGZHI_MIYUJILU"] = {
    text = "确定要清空所有的密语记录吗？",
    button1 = "确定",
    button2 = "取消",
    OnAccept = function()
        GetWhisperGlobalDB().record = {{}, {}}
        ClearDBCache()
        WhisperRecord.UpdateWhisperList()

        NotifyWhisperButtonUpdate()
    end,
    timeout = 0,
    whileDead = true,
    hideOnEscape = true,
    preferredIndex = 3,
}

StaticPopupDialogs["DELETE_WHISPER_RECORD"] = {
    text = "确定要删除与 %s 的所有密语记录吗？",
    button1 = "确定",
    button2 = "取消",
    OnAccept = function(self, playerName)
        if playerName then
            local globalDB = GetWhisperGlobalDB()
            if globalDB.record[1][playerName] then
                globalDB.record[1][playerName] = nil
                ClearDBCache()
                WhisperRecord.UpdateWhisperList()

                NotifyWhisperButtonUpdate()
            end
        end
    end,
    timeout = 0,
    whileDead = true,
    hideOnEscape = true,
    preferredIndex = 3,
}



-- 主要接口函数
function WhisperRecord.ToggleWhisperRecordFrame()
    local WanMY = WhisperRecord.WanMY or WhisperRecord.CreateWhisperRecordFrame()
    if WanMY:IsShown() then WanMY:Hide() else WanMY:Show(); WhisperRecord.UpdateWhisperList() end
end

function WhisperRecord.ShowWhisperRecordFrame()
    local WanMY = WhisperRecord.WanMY or WhisperRecord.CreateWhisperRecordFrame()
    WanMY:Show()
    WhisperRecord.UpdateWhisperList()
end

function WhisperRecord.HideWhisperRecordFrame()
    local WanMY = WhisperRecord.WanMY
    if WanMY then WanMY:Hide(); WhisperRecord.ClearSelectedState() end
end

-- 注册时间戳点击处理器
local function RegisterTimestampClickHandler()
    -- 注册自定义超链接处理器
    local originalSetItemRef = SetItemRef
    SetItemRef = function(link, text, button, chatFrame)
        if link and link:match("^wchat:copy:") then
            local messageId = link:match("^wchat:copy:(.+)$")
            if messageId and WChat.CopySingleMessage then
                WChat:CopySingleMessage(messageId)
                return
            end
        end
        -- 调用原始函数处理其他链接
        return originalSetItemRef(link, text, button, chatFrame)
    end
end

-- 初始化时间戳点击功能
C_Timer.After(1, function()
    RegisterTimestampClickHandler()
end)

-- 时间戳颜色选择器（使用WoW标准颜色选择器）
function WChat:ShowTimestampColorPicker()
    local config = GetConfig()
    local currentColor = config.TimestampColor or {r = 255, g = 20, b = 147}

    -- 转换为0-1范围的颜色值
    local r, g, b = currentColor.r / 255, currentColor.g / 255, currentColor.b / 255

    -- 使用WoW标准颜色选择器
    ColorPickerFrame:SetColorRGB(r, g, b)
    ColorPickerFrame.hasOpacity = false
    ColorPickerFrame.previousValues = {r, g, b}

    -- 颜色更新函数
    local function updateColor(r, g, b)
        config.TimestampColor = {r = math.floor(r * 255 + 0.5), g = math.floor(g * 255 + 0.5), b = math.floor(b * 255 + 0.5)}
        if WChat.OnConfigChanged and WChat.OnConfigChanged.TimestampColor then
            WChat.OnConfigChanged.TimestampColor(config.TimestampColor)
        end
    end

    -- 设置颜色选择器回调
    ColorPickerFrame.func = function() updateColor(ColorPickerFrame:GetColorRGB()) end
    ColorPickerFrame.swatchFunc = ColorPickerFrame.func
    ColorPickerFrame.cancelFunc = function(prev) if prev then updateColor(prev[1], prev[2], prev[3]) end end

    -- 显示颜色选择器
    ColorPickerFrame:Show()
end

